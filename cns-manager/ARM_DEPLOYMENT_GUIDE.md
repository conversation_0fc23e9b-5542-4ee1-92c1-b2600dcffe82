# CNS 流量控制系统 ARM 部署指南

## 🎯 概述

本指南将帮助您在ARM架构设备上编译和部署CNS流量控制系统，支持各种ARM设备包括树莓派、ARM服务器、ARM开发板等。

## 🏗️ 支持的ARM架构

### ARM64 (aarch64)
- **树莓派**: 树莓派4、树莓派5
- **服务器**: AWS Graviton、华为鲲鹏、飞腾等
- **开发板**: Jetson Nano、Rock Pi等
- **手机/平板**: Android ARM64设备

### ARM32 (armv7l)
- **树莓派**: 树莓派2、树莓派3
- **开发板**: BeagleBone、Orange Pi等
- **嵌入式设备**: 路由器、NAS等

## 🛠️ 编译方法

### 方法一：使用编译脚本 (推荐)

#### Windows环境
```powershell
# 运行PowerShell编译脚本
.\build_arm.ps1
```

#### Linux/macOS环境
```bash
# 给脚本执行权限
chmod +x build_arm.sh

# 运行编译脚本
./build_arm.sh
```

### 方法二：使用Makefile
```bash
# 编译ARM64 Linux版本
make arm64

# 编译ARM32 Linux版本
make arm32

# 编译所有ARM版本
make arm-all

# 编译树莓派专用版本
make raspberry

# 创建完整发布包
make release
```

### 方法三：手动编译
```bash
# 安装依赖
go mod tidy

# ARM64 Linux
GOOS=linux GOARCH=arm64 CGO_ENABLED=0 go build -ldflags="-s -w" -o cns-arm64-linux

# ARM32 Linux
GOOS=linux GOARCH=arm CGO_ENABLED=0 go build -ldflags="-s -w" -o cns-arm32-linux

# ARM64 Windows
GOOS=windows GOARCH=arm64 CGO_ENABLED=0 go build -ldflags="-s -w" -o cns-arm64-windows.exe

# ARM32 Windows
GOOS=windows GOARCH=arm CGO_ENABLED=0 go build -ldflags="-s -w" -o cns-arm32-windows.exe
```

## 📦 部署步骤

### 1. 准备文件
编译完成后，您需要以下文件：
```
部署文件/
├── cns-arm64-linux          # ARM64可执行文件
├── config_arm.json          # ARM优化配置
├── start_arm_linux.sh       # Linux启动脚本
└── TRAFFIC_CONTROL_README.md # 使用文档
```

### 2. 传输到ARM设备
```bash
# 使用scp传输文件
scp cns-arm64-linux config_arm.json start_arm_linux.sh user@arm-device:/home/<USER>/cns/

# 或使用rsync
rsync -av cns-arm64-linux config_arm.json start_arm_linux.sh user@arm-device:/home/<USER>/cns/
```

### 3. 在ARM设备上部署
```bash
# 连接到ARM设备
ssh user@arm-device

# 进入部署目录
cd /home/<USER>/cns

# 设置执行权限
chmod +x cns-arm64-linux
chmod +x start_arm_linux.sh

# 启动服务
./start_arm_linux.sh
```

## 🍓 树莓派专用部署

### 系统要求
- **操作系统**: Raspberry Pi OS (推荐) 或 Ubuntu
- **内存**: 最少512MB，推荐1GB+
- **存储**: 最少100MB可用空间

### 快速部署脚本
```bash
#!/bin/bash
# 树莓派一键部署脚本

echo "🍓 树莓派CNS部署脚本"

# 更新系统
sudo apt update

# 创建部署目录
mkdir -p ~/cns
cd ~/cns

# 下载文件 (假设已编译好)
# 这里需要替换为实际的下载地址
# wget https://your-server.com/cns-arm64-linux
# wget https://your-server.com/config_arm.json

# 设置权限
chmod +x cns-arm64-linux

# 创建systemd服务
sudo tee /etc/systemd/system/cns.service > /dev/null <<EOF
[Unit]
Description=CNS Traffic Control System
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>/cns
ExecStart=/home/<USER>/cns/cns-arm64-linux -json=config_arm.json
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl enable cns
sudo systemctl start cns

echo "✅ 树莓派部署完成"
echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):8081"
```

## ⚙️ ARM优化配置

### config_arm.json 说明
```json
{
    "Tcp_timeout": 300,
    "Udp_timeout": 30,
    "Listen_addr": [":1254"],
    "proxy_key": "Host",
    "encrypt_password": "password",
    "Enable_dns_tcpOverUdp": true,
    "Enable_httpDNS": true,
    "Enable_TFO": false,          // ARM设备通常禁用TFO
    "Enable_traffic_control": true,
    "API_port": ":8080"
}
```

### 性能优化建议
1. **内存优化**: ARM设备内存有限，建议定期清理日志
2. **CPU优化**: 避免过多并发连接
3. **存储优化**: 使用SSD或高速SD卡
4. **网络优化**: 确保网络带宽充足

## 🔧 常见问题解决

### 1. 编译错误
```bash
# 错误: CGO相关错误
# 解决: 禁用CGO
export CGO_ENABLED=0

# 错误: 交叉编译工具链缺失
# Ubuntu/Debian解决方案:
sudo apt install gcc-arm-linux-gnueabihf gcc-aarch64-linux-gnu

# CentOS/RHEL解决方案:
sudo yum install gcc-arm-linux-gnu gcc-aarch64-linux-gnu
```

### 2. 运行时错误
```bash
# 错误: 权限不足
chmod +x cns-arm64-linux

# 错误: 端口被占用
sudo netstat -tlnp | grep :1254
sudo kill -9 <PID>

# 错误: 配置文件未找到
cp config.json config_arm.json
```

### 3. 性能问题
```bash
# 检查系统资源
htop
free -h
df -h

# 调整配置
# 减少Tcp_timeout和Udp_timeout
# 限制并发连接数
```

## 📊 监控和维护

### 系统监控
```bash
# 检查服务状态
sudo systemctl status cns

# 查看日志
sudo journalctl -u cns -f

# 检查资源使用
top -p $(pgrep cns)
```

### 性能监控
```bash
# 网络连接
ss -tlnp | grep cns

# 内存使用
cat /proc/$(pgrep cns)/status | grep VmRSS

# CPU使用
cat /proc/$(pgrep cns)/stat
```

## 🚀 自动化部署

### Docker部署 (推荐)
```dockerfile
# Dockerfile.arm64
FROM arm64v8/alpine:latest

RUN apk add --no-cache ca-certificates

WORKDIR /app

COPY cns-arm64-linux /app/cns
COPY config_arm.json /app/config.json

RUN chmod +x /app/cns

EXPOSE 1254 8080 8081

CMD ["./cns", "-json=config.json"]
```

```bash
# 构建镜像
docker build -f Dockerfile.arm64 -t cns-arm64 .

# 运行容器
docker run -d -p 1254:1254 -p 8080:8080 -p 8081:8081 cns-arm64
```

### Kubernetes部署
```yaml
# cns-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cns-arm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cns-arm
  template:
    metadata:
      labels:
        app: cns-arm
    spec:
      containers:
      - name: cns
        image: cns-arm64:latest
        ports:
        - containerPort: 1254
        - containerPort: 8080
        - containerPort: 8081
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: cns-service
spec:
  selector:
    app: cns-arm
  ports:
  - name: proxy
    port: 1254
    targetPort: 1254
  - name: api
    port: 8080
    targetPort: 8080
  - name: web
    port: 8081
    targetPort: 8081
  type: LoadBalancer
```

## 📱 移动设备支持

### Android设备
1. 使用Termux环境
2. 编译ARM64版本
3. 配置网络权限

### iOS设备
1. 需要越狱环境
2. 使用iSH或类似工具
3. 编译ARM64版本

## 🔒 安全建议

### ARM设备安全
1. **防火墙配置**: 只开放必要端口
2. **用户权限**: 使用非root用户运行
3. **自动更新**: 定期更新系统和软件
4. **监控告警**: 设置异常监控

### 网络安全
1. **VPN隧道**: 在公网环境使用VPN
2. **证书验证**: 启用TLS证书验证
3. **访问控制**: 限制API访问来源
4. **日志审计**: 记录所有访问日志

## 📞 技术支持

### 常用命令
```bash
# 查看ARM架构
uname -m

# 查看系统信息
cat /proc/cpuinfo

# 查看内存信息
cat /proc/meminfo

# 查看网络接口
ip addr show
```

### 故障排除
1. 检查编译目标架构是否匹配
2. 确认配置文件格式正确
3. 验证网络端口可用性
4. 查看系统日志错误信息

---

**部署完成后访问地址**:
- Web管理界面: http://设备IP:8081
- API服务: http://设备IP:8080
- 代理服务: 设备IP:1254
