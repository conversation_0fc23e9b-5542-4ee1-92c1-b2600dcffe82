#!/bin/bash

# CNS 流量控制系统 ARM 编译脚本

echo "🚀 CNS ARM 编译脚本"
echo "==================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"
    
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装，请先安装Go${NC}"
        echo -e "${BLUE}安装方法:${NC}"
        echo "  Ubuntu/Debian: sudo apt install golang-go"
        echo "  CentOS/RHEL: sudo yum install golang"
        echo "  或从官网下载: https://golang.org/dl/"
        exit 1
    fi
    
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境检查通过: $GO_VERSION${NC}"
}

# 检查架构
check_arch() {
    echo -e "${YELLOW}🔍 检查系统架构...${NC}"
    
    CURRENT_OS=$(go env GOOS)
    CURRENT_ARCH=$(go env GOARCH)
    
    echo -e "${BLUE}当前系统: $CURRENT_OS/$CURRENT_ARCH${NC}"
    
    # 检测ARM架构
    if [[ "$CURRENT_ARCH" == "arm"* ]] || [[ "$CURRENT_ARCH" == "aarch64" ]]; then
        echo -e "${GREEN}✅ 检测到ARM架构${NC}"
        IS_ARM=true
    else
        echo -e "${YELLOW}⚠️  当前不是ARM架构，将进行交叉编译${NC}"
        IS_ARM=false
    fi
}

# 安装依赖
install_deps() {
    echo -e "${YELLOW}📥 安装Go依赖...${NC}"
    
    # 初始化go.mod（如果不存在）
    if [ ! -f "go.mod" ]; then
        go mod init cns
    fi
    
    # 下载依赖
    go mod tidy
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 依赖安装完成${NC}"
    else
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
}

# 编译函数
build_for_target() {
    local target_os=$1
    local target_arch=$2
    local output_name=$3
    
    echo -e "${YELLOW}🔨 编译 $target_os/$target_arch...${NC}"
    
    # 设置环境变量
    export GOOS=$target_os
    export GOARCH=$target_arch
    export CGO_ENABLED=1
    
    # 对于交叉编译，需要设置CGO
    if [ "$target_os" != "$(go env GOHOSTOS)" ] || [ "$target_arch" != "$(go env GOHOSTARCH)" ]; then
        echo -e "${BLUE}💡 交叉编译模式${NC}"
        
        # 根据目标架构设置交叉编译工具
        case "$target_arch" in
            "arm")
                export CC=arm-linux-gnueabihf-gcc
                ;;
            "arm64")
                export CC=aarch64-linux-gnu-gcc
                ;;
        esac
        
        # 检查交叉编译工具
        if [ ! -z "$CC" ] && ! command -v $CC &> /dev/null; then
            echo -e "${YELLOW}⚠️  交叉编译工具 $CC 未找到，尝试禁用CGO${NC}"
            export CGO_ENABLED=0
        fi
    fi
    
    # 编译
    go build -ldflags="-s -w" -o "$output_name"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 编译成功: $output_name${NC}"
        
        # 显示文件信息
        if [ -f "$output_name" ]; then
            file_size=$(du -h "$output_name" | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"
            
            # 显示文件类型
            if command -v file &> /dev/null; then
                file_type=$(file "$output_name")
                echo -e "${BLUE}📋 文件类型: $file_type${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ 编译失败${NC}"
        return 1
    fi
}

# 主编译函数
main_build() {
    echo -e "${YELLOW}🎯 选择编译目标...${NC}"
    
    echo "请选择编译目标:"
    echo "1) ARM 32位 (arm/linux)"
    echo "2) ARM 64位 (arm64/linux)"
    echo "3) 当前系统架构"
    echo "4) 编译所有ARM架构"
    echo "5) 自定义架构"
    
    read -p "请输入选择 (1-5): " choice
    
    case $choice in
        1)
            build_for_target "linux" "arm" "cns-arm32"
            ;;
        2)
            build_for_target "linux" "arm64" "cns-arm64"
            ;;
        3)
            current_os=$(go env GOOS)
            current_arch=$(go env GOARCH)
            build_for_target "$current_os" "$current_arch" "cns-native"
            ;;
        4)
            echo -e "${BLUE}📦 编译所有ARM架构...${NC}"
            build_for_target "linux" "arm" "cns-arm32"
            build_for_target "linux" "arm64" "cns-arm64"
            ;;
        5)
            read -p "请输入目标OS (如: linux): " target_os
            read -p "请输入目标架构 (如: arm64): " target_arch
            read -p "请输入输出文件名: " output_name
            build_for_target "$target_os" "$target_arch" "$output_name"
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            exit 1
            ;;
    esac
}

# 安装交叉编译工具
install_cross_tools() {
    echo -e "${YELLOW}🛠️  检查交叉编译工具...${NC}"
    
    # 检测系统类型
    if command -v apt &> /dev/null; then
        # Ubuntu/Debian
        echo -e "${BLUE}检测到 Ubuntu/Debian 系统${NC}"
        echo "安装交叉编译工具:"
        echo "  sudo apt update"
        echo "  sudo apt install gcc-arm-linux-gnueabihf gcc-aarch64-linux-gnu"
        
        read -p "是否现在安装? (y/N): " install_now
        if [[ $install_now =~ ^[Yy]$ ]]; then
            sudo apt update
            sudo apt install gcc-arm-linux-gnueabihf gcc-aarch64-linux-gnu
        fi
        
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        echo -e "${BLUE}检测到 CentOS/RHEL 系统${NC}"
        echo "安装交叉编译工具:"
        echo "  sudo yum install gcc-arm-linux-gnu gcc-aarch64-linux-gnu"
        
    elif command -v pacman &> /dev/null; then
        # Arch Linux
        echo -e "${BLUE}检测到 Arch Linux 系统${NC}"
        echo "安装交叉编译工具:"
        echo "  sudo pacman -S arm-linux-gnueabihf-gcc aarch64-linux-gnu-gcc"
        
    else
        echo -e "${YELLOW}⚠️  未识别的系统，请手动安装交叉编译工具${NC}"
    fi
}

# 创建配置文件
create_config() {
    echo -e "${YELLOW}⚙️  创建ARM优化配置...${NC}"
    
    cat > config_arm.json << 'EOF'
{
    "Tcp_timeout": 300,
    "Udp_timeout": 30,
    "Listen_addr": [":1254"],
    "proxy_key": "Host",
    "encrypt_password": "password",
    "Enable_dns_tcpOverUdp": true,
    "Enable_httpDNS": true,
    "Enable_TFO": false,
    "Enable_traffic_control": true,
    "API_port": ":8080",
    "Tls": {
        "listen_addr": [":8978"],
        "AutoCertHosts": ["localhost"]
    }
}
EOF
    
    echo -e "${GREEN}✅ ARM配置文件已创建: config_arm.json${NC}"
}

# 创建启动脚本
create_startup_script() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    cat > start_arm.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (ARM)"
echo "================================"

# 检查可执行文件
if [ -f "cns-arm64" ]; then
    CNS_BINARY="./cns-arm64"
elif [ -f "cns-arm32" ]; then
    CNS_BINARY="./cns-arm32"
elif [ -f "cns-native" ]; then
    CNS_BINARY="./cns-native"
else
    echo "❌ 未找到CNS可执行文件"
    echo "请先运行编译脚本: ./build_arm.sh"
    exit 1
fi

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
EOF
    
    chmod +x start_arm.sh
    echo -e "${GREEN}✅ 启动脚本已创建: start_arm.sh${NC}"
}

# 显示使用说明
show_usage() {
    echo -e "${GREEN}📋 编译完成！${NC}"
    echo ""
    echo -e "${BLUE}🚀 使用方法:${NC}"
    echo "1. 启动服务:"
    echo "   ./start_arm.sh"
    echo ""
    echo "2. 手动启动:"
    echo "   ./cns-arm64 -json=config_arm.json"
    echo ""
    echo "3. 访问管理界面:"
    echo "   http://localhost:8081"
    echo ""
    echo "4. API文档:"
    echo "   查看 TRAFFIC_CONTROL_README.md"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "- 确保防火墙允许相应端口访问"
    echo "- 在ARM设备上运行时可能需要sudo权限"
    echo "- 建议在后台运行: nohup ./start_arm.sh &"
}

# 主函数
main() {
    echo -e "${BLUE}欢迎使用 CNS ARM 编译脚本！${NC}"
    echo ""
    
    # 检查Go环境
    check_go
    
    # 检查架构
    check_arch
    
    # 询问是否安装交叉编译工具
    if [ "$IS_ARM" = false ]; then
        read -p "是否需要安装交叉编译工具? (y/N): " install_tools
        if [[ $install_tools =~ ^[Yy]$ ]]; then
            install_cross_tools
        fi
    fi
    
    # 安装依赖
    install_deps
    
    # 创建配置文件
    create_config
    
    # 主编译
    main_build
    
    # 创建启动脚本
    create_startup_script
    
    # 显示使用说明
    show_usage
}

# 运行主函数
main "$@"
