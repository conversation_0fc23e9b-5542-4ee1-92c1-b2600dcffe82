#!/bin/bash

# CNS 完整编译脚本 - 支持AMD64和ARM，解决CGO问题

echo "🚀 CNS 完整编译脚本"
echo "=================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"
    
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装${NC}"
        return 1
    fi
    
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境: $GO_VERSION${NC}"
    return 0
}

# 设置Go代理
setup_proxy() {
    echo -e "${YELLOW}🌐 设置Go代理...${NC}"
    
    export GOPROXY=https://goproxy.cn,direct
    export GOSUMDB=sum.golang.google.cn
    export GO111MODULE=on
    
    echo -e "${BLUE}GOPROXY: $GOPROXY${NC}"
}

# 修复依赖
fix_dependencies() {
    echo -e "${YELLOW}🧹 修复依赖...${NC}"
    
    rm -f go.sum
    go clean -modcache
    go mod tidy
    go get github.com/google/uuid@v1.3.0
    go get github.com/gorilla/mux@v1.8.0
    go get github.com/mattn/go-sqlite3@v1.14.17
    go mod download
    
    if go mod verify; then
        echo -e "${GREEN}✅ 依赖修复成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 依赖修复失败${NC}"
        return 1
    fi
}

# 创建纯Go数据库版本
create_pure_go_version() {
    echo -e "${YELLOW}📝 创建纯Go版本...${NC}"
    
    # 备份原文件
    cp db.go db_sqlite.go
    cp go.mod go.mod.bak
    
    # 创建纯Go的go.mod
    cat > go.mod << 'EOF'
module cns

go 1.18

require (
    github.com/google/uuid v1.3.0
    github.com/gorilla/mux v1.8.0
)
EOF

    # 创建纯Go数据库
    cat > db.go << 'EOF'
package main

import (
    "encoding/json"
    "log"
    "os"
    "sync"
    "time"
)

type MemoryDB struct {
    Users map[string]*User `json:"users"`
    mutex sync.RWMutex
}

type User struct {
    UserID    string     `json:"user_id"`
    Username  string     `json:"username"`
    Password  string     `json:"password"`
    Quota     int64      `json:"quota"`
    Upload    int64      `json:"upload"`
    Download  int64      `json:"download"`
    CreatedAt time.Time  `json:"created_at"`
    ExpireAt  *time.Time `json:"expire_at,omitempty"`
    Status    int        `json:"status"`
    WechatID  string     `json:"wechat_id"`
}

var (
    memDB  *MemoryDB
    dbFile = "cns_users.json"
)

func initDatabase() {
    memDB = &MemoryDB{
        Users: make(map[string]*User),
    }
    
    if data, err := os.ReadFile(dbFile); err == nil {
        json.Unmarshal(data, memDB)
    }
    
    log.Println("数据库初始化完成 (纯Go版本)")
    
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        for range ticker.C {
            saveDatabase()
        }
    }()
}

func saveDatabase() {
    memDB.mutex.RLock()
    data, _ := json.MarshalIndent(memDB, "", "  ")
    memDB.mutex.RUnlock()
    os.WriteFile(dbFile, data, 0644)
}

func addUser(userID, username, password string, quota int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    memDB.Users[userID] = &User{
        UserID:    userID,
        Username:  username,
        Password:  password,
        Quota:     quota,
        CreatedAt: time.Now(),
        Status:    1,
    }
    return nil
}

func getUserTraffic(userID string) (upload, download, quota int64, err error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return 0, 0, 1073741824, nil
    }
    return user.Upload, user.Download, user.Quota, nil
}

func resetUserTraffic(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    if user, exists := memDB.Users[userID]; exists {
        user.Upload = 0
        user.Download = 0
    }
    return nil
}

func deleteUser(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    delete(memDB.Users, userID)
    return nil
}

func bindWechatID(userID, wechatID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    if user, exists := memDB.Users[userID]; exists {
        user.WechatID = wechatID
    }
    return nil
}

func getUserIDByWechatID(wechatID string) (string, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    for _, user := range memDB.Users {
        if user.WechatID == wechatID {
            return user.UserID, nil
        }
    }
    return "", nil
}

func logConnection(userID, clientIP, targetHost string) (int64, error) {
    return 1, nil
}

func updateConnectionLog(logID int64, upload, download int64) error {
    return nil
}

func getUserConnectionHistory(userID string, limit int) ([]map[string]interface{}, error) {
    return []map[string]interface{}{}, nil
}

func updateDailyTrafficStats(userID string, upload, download int64) error {
    return nil
}

func getUserDailyStats(userID string, days int) ([]map[string]interface{}, error) {
    return []map[string]interface{}{}, nil
}

func updateUserInfo(userID string, quota *int64, status *int) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    if user, exists := memDB.Users[userID]; exists {
        if quota != nil {
            user.Quota = *quota
        }
        if status != nil {
            user.Status = *status
        }
    }
    return nil
}

func setUserExpire(userID string, expireTime time.Time) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    if user, exists := memDB.Users[userID]; exists {
        user.ExpireAt = &expireTime
    }
    return nil
}

func getTotalUserCount() (int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    return len(memDB.Users), nil
}

func getUserList(page, limit int) ([]map[string]interface{}, int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    total := len(memDB.Users)
    var users []map[string]interface{}
    
    for _, user := range memDB.Users {
        userMap := map[string]interface{}{
            "user_id":      user.UserID,
            "username":     user.Username,
            "quota":        user.Quota,
            "upload":       user.Upload,
            "download":     user.Download,
            "created_at":   user.CreatedAt.Format("2006-01-02 15:04:05"),
            "status":       user.Status,
            "wechat_id":    user.WechatID,
            "used_percent": float64(user.Upload+user.Download) / float64(user.Quota) * 100,
        }
        users = append(users, userMap)
        if len(users) >= limit {
            break
        }
    }
    
    return users, total, nil
}

func updateUserTrafficInDB(userID string, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    if user, exists := memDB.Users[userID]; exists {
        user.Upload += upload
        user.Download += download
    }
    return nil
}
EOF

    echo -e "${GREEN}✅ 纯Go版本创建完成${NC}"
}

# 恢复SQLite版本
restore_sqlite_version() {
    if [ -f "db_sqlite.go" ]; then
        mv db_sqlite.go db.go
    fi
    if [ -f "go.mod.bak" ]; then
        mv go.mod.bak go.mod
    fi
}

# 编译AMD64版本
build_amd64() {
    echo -e "${YELLOW}🔨 编译AMD64版本...${NC}"
    
    export GOOS=linux
    export GOARCH=amd64
    export CGO_ENABLED=1
    
    go build -ldflags="-s -w" -o cns-amd64-linux
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ AMD64编译成功: cns-amd64-linux${NC}"
        return 0
    else
        echo -e "${RED}❌ AMD64编译失败${NC}"
        return 1
    fi
}

# 编译ARM64版本（纯Go）
build_arm64_pure() {
    echo -e "${YELLOW}🔨 编译ARM64版本 (纯Go)...${NC}"
    
    create_pure_go_version
    
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    go mod tidy
    go build -ldflags="-s -w" -o cns-arm64-linux-pure
    
    restore_sqlite_version
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM64纯Go版本编译成功: cns-arm64-linux-pure${NC}"
        return 0
    else
        echo -e "${RED}❌ ARM64纯Go版本编译失败${NC}"
        return 1
    fi
}

# 编译ARM32版本（纯Go）
build_arm32_pure() {
    echo -e "${YELLOW}🔨 编译ARM32版本 (纯Go)...${NC}"
    
    create_pure_go_version
    
    export GOOS=linux
    export GOARCH=arm
    export CGO_ENABLED=0
    
    go mod tidy
    go build -ldflags="-s -w" -o cns-arm32-linux-pure
    
    restore_sqlite_version
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM32纯Go版本编译成功: cns-arm32-linux-pure${NC}"
        return 0
    else
        echo -e "${RED}❌ ARM32纯Go版本编译失败${NC}"
        return 1
    fi
}

# 创建启动脚本
create_startup_scripts() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    cat > start_cns.sh << 'EOF'
#!/bin/bash
echo "🚀 启动 CNS 流量控制系统"

# 自动检测架构
ARCH=$(uname -m)
case $ARCH in
    "x86_64")
        if [ -f "cns-amd64-linux" ]; then
            CNS_BINARY="./cns-amd64-linux"
            echo "使用 AMD64 版本 (支持SQLite)"
        else
            echo "❌ 未找到AMD64可执行文件"
            exit 1
        fi
        ;;
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux-pure" ]; then
            CNS_BINARY="./cns-arm64-linux-pure"
            echo "使用 ARM64 纯Go版本"
        else
            echo "❌ 未找到ARM64可执行文件"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux-pure" ]; then
            CNS_BINARY="./cns-arm32-linux-pure"
            echo "使用 ARM32 纯Go版本"
        else
            echo "❌ 未找到ARM32可执行文件"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        exit 1
        ;;
esac

chmod +x $CNS_BINARY

CONFIG_FILE="config_arm.json"
if [ ! -f "$CONFIG_FILE" ]; then
    CONFIG_FILE="config.json"
fi

echo "配置文件: $CONFIG_FILE"
echo "代理服务: localhost:1254"
echo "API服务: http://localhost:8080"
echo "Web管理: http://localhost:8081"
echo ""

$CNS_BINARY -json=$CONFIG_FILE
EOF

    chmod +x start_cns.sh
    echo -e "${GREEN}✅ 启动脚本创建完成${NC}"
}

# 显示结果
show_results() {
    echo -e "${GREEN}📋 编译结果${NC}"
    echo "=============="
    
    if [ -f "cns-amd64-linux" ]; then
        size=$(du -h cns-amd64-linux | cut -f1)
        echo -e "${GREEN}✅ AMD64 (SQLite): cns-amd64-linux ($size)${NC}"
    fi
    
    if [ -f "cns-arm64-linux-pure" ]; then
        size=$(du -h cns-arm64-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM64 (纯Go): cns-arm64-linux-pure ($size)${NC}"
    fi
    
    if [ -f "cns-arm32-linux-pure" ]; then
        size=$(du -h cns-arm32-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM32 (纯Go): cns-arm32-linux-pure ($size)${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}🚀 使用方法:${NC}"
    echo "  ./start_cns.sh    # 自动选择架构"
    echo ""
    echo -e "${YELLOW}💡 说明:${NC}"
    echo "  - AMD64版本支持SQLite数据库"
    echo "  - ARM版本使用纯Go内存数据库"
    echo "  - 数据保存在 cns_users.json 文件中"
}

# 主函数
main() {
    echo -e "${BLUE}开始编译CNS多架构版本...${NC}"
    
    if ! check_go; then
        exit 1
    fi
    
    setup_proxy
    
    if ! fix_dependencies; then
        echo -e "${RED}❌ 依赖修复失败${NC}"
        exit 1
    fi
    
    # 编译各个版本
    build_amd64
    build_arm64_pure
    build_arm32_pure
    
    # 创建启动脚本
    create_startup_scripts
    
    # 显示结果
    show_results
    
    echo ""
    echo -e "${GREEN}🎉 编译完成！${NC}"
}

main "$@"
