package main

import (
    "database/sql"
    "log"
    "time"
    _ "github.com/mattn/go-sqlite3"
)

// 初始化数据库
func initDatabase() {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        log.Fatal("数据库连接错误:", err)
    }
    defer db.Close()

    // 创建用户表
    _, err = db.Exec(`
    CREATE TABLE IF NOT EXISTS users (
        user_id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        quota BIGINT DEFAULT 1073741824, -- 默认1GB
        upload BIGINT DEFAULT 0,
        download BIGINT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expire_at TIMESTAMP,
        status INTEGER DEFAULT 1,
        wechat_id TEXT
    )`)

    if err != nil {
        log.Fatal("创建用户表失败:", err)
    }

    // 创建连接日志表
    _, err = db.Exec(`
    CREATE TABLE IF NOT EXISTS connection_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        client_ip TEXT,
        target_host TEXT,
        connect_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        disconnect_time TIMESTAMP,
        upload BIGINT DEFAULT 0,
        download BIGINT DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users(user_id)
    )`)

    if err != nil {
        log.Fatal("创建连接日志表失败:", err)
    }

    // 创建流量统计表
    _, err = db.Exec(`
    CREATE TABLE IF NOT EXISTS traffic_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        date DATE,
        upload BIGINT DEFAULT 0,
        download BIGINT DEFAULT 0,
        connections INTEGER DEFAULT 0,
        UNIQUE(user_id, date),
        FOREIGN KEY (user_id) REFERENCES users(user_id)
    )`)

    if err != nil {
        log.Fatal("创建流量统计表失败:", err)
    }

    log.Println("数据库初始化完成")
}

// 添加用户
func addUser(userID, username, password string, quota int64) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()
    
    _, err = db.Exec("INSERT INTO users (user_id, username, password, quota) VALUES (?, ?, ?, ?)",
                    userID, username, password, quota)
    return err
}

// 删除用户
func deleteUser(userID string) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()
    
    _, err = db.Exec("DELETE FROM users WHERE user_id = ?", userID)
    return err
}

// 重置用户流量
func resetUserTraffic(userID string) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()
    
    _, err = db.Exec("UPDATE users SET upload = 0, download = 0 WHERE user_id = ?", userID)
    return err
}

// 获取用户流量信息
func getUserTraffic(userID string) (upload, download, quota int64, err error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return 0, 0, 0, err
    }
    defer db.Close()
    
    err = db.QueryRow("SELECT upload, download, quota FROM users WHERE user_id = ?", userID).Scan(&upload, &download, &quota)
    return
}

// 绑定微信ID
func bindWechatID(userID, wechatID string) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()
    
    _, err = db.Exec("UPDATE users SET wechat_id = ? WHERE user_id = ?", wechatID, userID)
    return err
}

// 根据微信ID获取用户ID
func getUserIDByWechatID(wechatID string) (string, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return "", err
    }
    defer db.Close()

    var userID string
    err = db.QueryRow("SELECT user_id FROM users WHERE wechat_id = ?", wechatID).Scan(&userID)
    return userID, err
}

// 记录连接日志
func logConnection(userID, clientIP, targetHost string) (int64, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return 0, err
    }
    defer db.Close()

    result, err := db.Exec("INSERT INTO connection_logs (user_id, client_ip, target_host) VALUES (?, ?, ?)",
                          userID, clientIP, targetHost)
    if err != nil {
        return 0, err
    }

    return result.LastInsertId()
}

// 更新连接日志（断开连接时）
func updateConnectionLog(logID int64, upload, download int64) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()

    _, err = db.Exec("UPDATE connection_logs SET disconnect_time = CURRENT_TIMESTAMP, upload = ?, download = ? WHERE id = ?",
                     upload, download, logID)
    return err
}

// 获取用户连接历史
func getUserConnectionHistory(userID string, limit int) ([]map[string]interface{}, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return nil, err
    }
    defer db.Close()

    query := `SELECT client_ip, target_host, connect_time, disconnect_time, upload, download
              FROM connection_logs WHERE user_id = ? ORDER BY connect_time DESC LIMIT ?`

    rows, err := db.Query(query, userID, limit)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var history []map[string]interface{}
    for rows.Next() {
        var clientIP, targetHost string
        var connectTime, disconnectTime sql.NullString
        var upload, download int64

        err := rows.Scan(&clientIP, &targetHost, &connectTime, &disconnectTime, &upload, &download)
        if err != nil {
            continue
        }

        record := map[string]interface{}{
            "client_ip":       clientIP,
            "target_host":     targetHost,
            "connect_time":    connectTime.String,
            "disconnect_time": disconnectTime.String,
            "upload":          upload,
            "download":        download,
        }
        history = append(history, record)
    }

    return history, nil
}

// 更新每日流量统计
func updateDailyTrafficStats(userID string, upload, download int64) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()

    today := time.Now().Format("2006-01-02")

    _, err = db.Exec(`INSERT INTO traffic_stats (user_id, date, upload, download, connections)
                      VALUES (?, ?, ?, ?, 1)
                      ON CONFLICT(user_id, date) DO UPDATE SET
                      upload = upload + ?, download = download + ?, connections = connections + 1`,
                     userID, today, upload, download, upload, download)

    return err
}

// 获取用户每日流量统计
func getUserDailyStats(userID string, days int) ([]map[string]interface{}, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return nil, err
    }
    defer db.Close()

    query := `SELECT date, upload, download, connections
              FROM traffic_stats WHERE user_id = ?
              ORDER BY date DESC LIMIT ?`

    rows, err := db.Query(query, userID, days)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var stats []map[string]interface{}
    for rows.Next() {
        var date string
        var upload, download int64
        var connections int

        err := rows.Scan(&date, &upload, &download, &connections)
        if err != nil {
            continue
        }

        stat := map[string]interface{}{
            "date":        date,
            "upload":      upload,
            "download":    download,
            "connections": connections,
            "total":       upload + download,
        }
        stats = append(stats, stat)
    }

    return stats, nil
}

// 更新用户信息
func updateUserInfo(userID string, quota *int64, status *int) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()

    if quota != nil {
        _, err = db.Exec("UPDATE users SET quota = ? WHERE user_id = ?", *quota, userID)
        if err != nil {
            return err
        }
    }

    if status != nil {
        _, err = db.Exec("UPDATE users SET status = ? WHERE user_id = ?", *status, userID)
        if err != nil {
            return err
        }
    }

    return nil
}

// 设置用户过期时间
func setUserExpire(userID string, expireTime time.Time) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()

    _, err = db.Exec("UPDATE users SET expire_at = ? WHERE user_id = ?", expireTime, userID)
    return err
}

// 获取总用户数
func getTotalUserCount() (int, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return 0, err
    }
    defer db.Close()

    var count int
    err = db.QueryRow("SELECT COUNT(*) FROM users").Scan(&count)
    return count, err
}

// 获取用户列表
func getUserList(page, limit int) ([]map[string]interface{}, int, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return nil, 0, err
    }
    defer db.Close()

    // 获取总数
    var total int
    err = db.QueryRow("SELECT COUNT(*) FROM users").Scan(&total)
    if err != nil {
        return nil, 0, err
    }

    // 获取用户列表
    offset := (page - 1) * limit
    query := `SELECT user_id, username, quota, upload, download, created_at, expire_at, status, wechat_id
              FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?`

    rows, err := db.Query(query, limit, offset)
    if err != nil {
        return nil, 0, err
    }
    defer rows.Close()

    var users []map[string]interface{}
    for rows.Next() {
        var userID, username, wechatID string
        var quota, upload, download int64
        var createdAt string
        var expireAt sql.NullString
        var status int

        err := rows.Scan(&userID, &username, &quota, &upload, &download, &createdAt, &expireAt, &status, &wechatID)
        if err != nil {
            continue
        }

        user := map[string]interface{}{
            "user_id":    userID,
            "username":   username,
            "quota":      quota,
            "upload":     upload,
            "download":   download,
            "created_at": createdAt,
            "expire_at":  expireAt.String,
            "status":     status,
            "wechat_id":  wechatID,
            "used_percent": float64(upload+download) / float64(quota) * 100,
        }
        users = append(users, user)
    }

    return users, total, nil
}