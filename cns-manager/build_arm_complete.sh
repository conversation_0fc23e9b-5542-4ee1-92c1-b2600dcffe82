#!/bin/bash

# CNS ARM 完整编译脚本 - 最终解决方案

echo "🔧 CNS ARM 完整编译脚本"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"

    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装${NC}"
        return 1
    fi

    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境: $GO_VERSION${NC}"
    return 0
}

# 设置Go代理
setup_proxy() {
    echo -e "${YELLOW}🌐 设置Go代理...${NC}"

    export GOPROXY=https://goproxy.cn,direct
    export GOSUMDB=sum.golang.google.cn
    export GO111MODULE=on

    echo -e "${BLUE}GOPROXY: $GOPROXY${NC}"
}

# 创建完整的ARM编译版本
create_complete_arm_version() {
    echo -e "${YELLOW}📁 创建完整的ARM编译版本...${NC}"

    # 创建临时编译目录
    BUILD_DIR="arm_complete_build"
    rm -rf $BUILD_DIR
    mkdir -p $BUILD_DIR

    # 进入编译目录
    cd $BUILD_DIR

    # 创建go.mod
    cat > go.mod << 'EOF'
module cns

go 1.18

require (
    github.com/google/uuid v1.3.0
    github.com/gorilla/mux v1.8.0
)
EOF

    cat > go.sum << 'EOF'
github.com/google/uuid v1.3.0 h1:t6JiXgmwXMjEs8VusXIJk2BXHsn+wx8BZdTaoZ5fu7I=
github.com/google/uuid v1.3.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=
EOF

    echo -e "${GREEN}✅ Go模块文件创建完成${NC}"

    # 创建主程序文件
    create_main_file

    # 创建数据库文件
    create_database_file

    # 创建API文件
    create_api_file

    # 创建Web管理文件
    create_web_admin_file

    # 创建网络处理文件
    create_network_files

    # 创建系统文件
    create_system_files

    echo -e "${GREEN}✅ 所有源文件创建完成${NC}"

    return 0
}

# 创建主程序文件
create_main_file() {
    cat > cns.go << 'EOF'
package main

import (
    "encoding/json"
    "flag"
    "fmt"
    "io/ioutil"
    "log"
    "os"
    "os/exec"
    "os/signal"
    "syscall"
    "time"
)

var appStartTime = time.Now()

type JsonConfig struct {
    Tls                                               TlsServer
    Listen_addr                                       []string
    Proxy_key, Udp_flag, Encrypt_password, Pid_path   string
    Tcp_timeout, Udp_timeout                          time.Duration
    Enable_dns_tcpOverUdp, Enable_httpDNS, Enable_TFO bool
    Enable_traffic_control                            bool
    API_port                                          string
}

var config = JsonConfig{
    Proxy_key:              "Host",
    Udp_flag:               "httpUDP",
    Tcp_timeout:            600,
    Udp_timeout:            30,
    Enable_traffic_control: true,
    API_port:               ":8080",
}

func jsonLoad(filename string, v *JsonConfig) {
    data, err := ioutil.ReadFile(filename)
    if err != nil {
        log.Fatal(err)
        return
    }
    err = json.Unmarshal(data, v)
    if err != nil {
        log.Fatal(err)
        return
    }
}

func pidSaveToFile(pidPath string) {
    fp, err := os.Create(pidPath)
    if err != nil {
        fmt.Println(err)
        return
    }
    fp.WriteString(fmt.Sprintf("%d", os.Getpid()))
    if err != nil {
        fmt.Println(err)
    }
    fp.Close()
}

func handleCmd() {
    var (
        err                 error
        jsonConfigPath      string
        help, enable_daemon bool
    )

    flag.StringVar(&jsonConfigPath, "json", "", "json config path")
    flag.BoolVar(&enable_daemon, "daemon", false, "daemon mode switch")
    flag.BoolVar(&help, "h", false, "")
    flag.BoolVar(&help, "help", false, "display this message")

    flag.Parse()
    if help == true {
        fmt.Println("CNS 流量控制系统 ARM版本\n纯Go实现，无CGO依赖")
        flag.Usage()
        os.Exit(0)
    }
    if jsonConfigPath == "" {
        flag.Usage()
        fmt.Println("\n\n未找到配置文件")
        os.Exit(1)
    }
    if enable_daemon == true {
        exec.Command(os.Args[0], []string(append(os.Args[1:], "-daemon=false"))...).Start()
        os.Exit(0)
    }
    jsonLoad(jsonConfigPath, &config)

    if err != nil {
        log.Println(err)
        os.Exit(1)
    }
    config.Enable_httpDNS = true
    config.Proxy_key = "\n" + config.Proxy_key + ": "
    CuteBi_XorCrypt_password = []byte(config.Encrypt_password)
    config.Tcp_timeout *= time.Second
    config.Udp_timeout *= time.Second
}

func initProcess() {
    log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
    handleCmd()
    signal.Ignore(syscall.SIGPIPE)
}

func main() {
    initProcess()

    // 初始化数据库
    if config.Enable_traffic_control {
        initDatabase()
        initTrafficControl()
        log.Println("流量控制系统已启用")
    }

    // 启动API服务
    startAPIServer()

    // 启动Web管理界面
    startWebAdmin()

    if config.Pid_path != "" {
        pidSaveToFile(config.Pid_path)
    }

    for i := len(config.Listen_addr) - 1; i >= 0; i-- {
        go startHttpTunnel(config.Listen_addr[i])
    }

    log.Printf("CNS服务器启动完成，监听地址: %v", config.Listen_addr)
    log.Printf("API服务启动，监听地址: %s", config.API_port)
    log.Printf("Web管理界面启动，访问地址: http://localhost:8081")

    select {}
}
EOF
}

# 创建数据库文件
create_database_file() {
    cat > db.go << 'EOF'
package main

import (
    "encoding/json"
    "log"
    "os"
    "sync"
    "time"
)

type MemoryDB struct {
    Users           map[string]*User           `json:"users"`
    ConnectionLogs  []ConnectionLog            `json:"connection_logs"`
    TrafficStats    []TrafficStat              `json:"traffic_stats"`
    mutex           sync.RWMutex
}

type User struct {
    UserID    string     `json:"user_id"`
    Username  string     `json:"username"`
    Password  string     `json:"password"`
    Quota     int64      `json:"quota"`
    Upload    int64      `json:"upload"`
    Download  int64      `json:"download"`
    CreatedAt time.Time  `json:"created_at"`
    ExpireAt  *time.Time `json:"expire_at,omitempty"`
    Status    int        `json:"status"`
    WechatID  string     `json:"wechat_id"`
}

type ConnectionLog struct {
    ID             int64     `json:"id"`
    UserID         string    `json:"user_id"`
    ClientIP       string    `json:"client_ip"`
    TargetHost     string    `json:"target_host"`
    ConnectTime    time.Time `json:"connect_time"`
    DisconnectTime *time.Time `json:"disconnect_time,omitempty"`
    Upload         int64     `json:"upload"`
    Download       int64     `json:"download"`
}

type TrafficStat struct {
    ID          int64  `json:"id"`
    UserID      string `json:"user_id"`
    Date        string `json:"date"`
    Upload      int64  `json:"upload"`
    Download    int64  `json:"download"`
    Connections int    `json:"connections"`
}

var (
    memDB      *MemoryDB
    dbFile     = "cns_users.json"
    nextLogID  = int64(1)
    nextStatID = int64(1)
)

func initDatabase() {
    memDB = &MemoryDB{
        Users:          make(map[string]*User),
        ConnectionLogs: make([]ConnectionLog, 0),
        TrafficStats:   make([]TrafficStat, 0),
    }

    if data, err := os.ReadFile(dbFile); err == nil {
        if err := json.Unmarshal(data, memDB); err != nil {
            log.Printf("加载数据库文件失败: %v", err)
        } else {
            log.Println("数据库文件加载成功")
        }
    }

    for _, log := range memDB.ConnectionLogs {
        if log.ID >= nextLogID {
            nextLogID = log.ID + 1
        }
    }
    for _, stat := range memDB.TrafficStats {
        if stat.ID >= nextStatID {
            nextStatID = stat.ID + 1
        }
    }

    log.Println("数据库初始化完成 (纯Go版本)")

    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        for range ticker.C {
            saveDatabase()
        }
    }()
}

func saveDatabase() {
    memDB.mutex.RLock()
    data, err := json.MarshalIndent(memDB, "", "  ")
    memDB.mutex.RUnlock()

    if err != nil {
        log.Printf("序列化数据库失败: %v", err)
        return
    }

    if err := os.WriteFile(dbFile, data, 0644); err != nil {
        log.Printf("保存数据库文件失败: %v", err)
    }
}

func addUser(userID, username, password string, quota int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    user := &User{
        UserID:    userID,
        Username:  username,
        Password:  password,
        Quota:     quota,
        Upload:    0,
        Download:  0,
        CreatedAt: time.Now(),
        Status:    1,
    }

    memDB.Users[userID] = user
    log.Printf("添加用户: %s", username)
    return nil
}

func getUserTraffic(userID string) (upload, download, quota int64, err error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return 0, 0, 1073741824, nil
    }

    return user.Upload, user.Download, user.Quota, nil
}

func resetUserTraffic(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }

    user.Upload = 0
    user.Download = 0
    log.Printf("重置用户流量: %s", userID)
    return nil
}

func deleteUser(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    delete(memDB.Users, userID)
    log.Printf("删除用户: %s", userID)
    return nil
}

func bindWechatID(userID, wechatID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }

    user.WechatID = wechatID
    log.Printf("绑定微信ID: %s -> %s", userID, wechatID)
    return nil
}

func getUserIDByWechatID(wechatID string) (string, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    for _, user := range memDB.Users {
        if user.WechatID == wechatID {
            return user.UserID, nil
        }
    }

    return "", nil
}

func authenticateUser(username, password string) (*User, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    for _, user := range memDB.Users {
        if user.Username == username && user.Password == password {
            return user, nil
        }
    }

    return nil, nil
}

func getUserByID(userID string) (*User, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return nil, nil
    }

    return user, nil
}

func logConnection(userID, clientIP, targetHost string) (int64, error) {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    logID := nextLogID
    nextLogID++

    log := ConnectionLog{
        ID:          logID,
        UserID:      userID,
        ClientIP:    clientIP,
        TargetHost:  targetHost,
        ConnectTime: time.Now(),
        Upload:      0,
        Download:    0,
    }

    memDB.ConnectionLogs = append(memDB.ConnectionLogs, log)
    return logID, nil
}

func updateConnectionLog(logID int64, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    for i := range memDB.ConnectionLogs {
        if memDB.ConnectionLogs[i].ID == logID {
            now := time.Now()
            memDB.ConnectionLogs[i].DisconnectTime = &now
            memDB.ConnectionLogs[i].Upload = upload
            memDB.ConnectionLogs[i].Download = download
            break
        }
    }

    return nil
}

func getUserConnectionHistory(userID string, limit int) ([]map[string]interface{}, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    var history []map[string]interface{}
    count := 0

    for i := len(memDB.ConnectionLogs) - 1; i >= 0 && count < limit; i-- {
        log := memDB.ConnectionLogs[i]
        if log.UserID == userID {
            record := map[string]interface{}{
                "client_ip":       log.ClientIP,
                "target_host":     log.TargetHost,
                "connect_time":    log.ConnectTime.Format("2006-01-02 15:04:05"),
                "disconnect_time": "",
                "upload":          log.Upload,
                "download":        log.Download,
            }

            if log.DisconnectTime != nil {
                record["disconnect_time"] = log.DisconnectTime.Format("2006-01-02 15:04:05")
            }

            history = append(history, record)
            count++
        }
    }

    return history, nil
}

func updateDailyTrafficStats(userID string, upload, download int64) error {
    return nil
}

func getUserDailyStats(userID string, days int) ([]map[string]interface{}, error) {
    return []map[string]interface{}{}, nil
}

func updateUserInfo(userID string, quota *int64, status *int) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }

    if quota != nil {
        user.Quota = *quota
    }

    if status != nil {
        user.Status = *status
    }

    return nil
}

func setUserExpire(userID string, expireTime time.Time) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }

    user.ExpireAt = &expireTime
    return nil
}

func getTotalUserCount() (int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    return len(memDB.Users), nil
}

func getUserList(page, limit int) ([]map[string]interface{}, int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()

    total := len(memDB.Users)
    var users []map[string]interface{}

    for _, user := range memDB.Users {
        userMap := map[string]interface{}{
            "user_id":      user.UserID,
            "username":     user.Username,
            "quota":        user.Quota,
            "upload":       user.Upload,
            "download":     user.Download,
            "created_at":   user.CreatedAt.Format("2006-01-02 15:04:05"),
            "expire_at":    "",
            "status":       user.Status,
            "wechat_id":    user.WechatID,
            "used_percent": float64(user.Upload+user.Download) / float64(user.Quota) * 100,
        }

        if user.ExpireAt != nil {
            userMap["expire_at"] = user.ExpireAt.Format("2006-01-02 15:04:05")
        }

        users = append(users, userMap)
        if len(users) >= limit {
            break
        }
    }

    return users, total, nil
}

func updateUserTrafficInDB(userID string, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()

    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }

    user.Upload += upload
    user.Download += download
    return nil
}
EOF
}