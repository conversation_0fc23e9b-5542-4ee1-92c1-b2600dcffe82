#!/bin/bash

# ARM设备CGO问题修复脚本

echo "🔧 ARM设备CGO问题修复脚本"
echo "========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查当前环境
check_environment() {
    echo -e "${YELLOW}🔍 检查当前环境...${NC}"
    
    # 检查架构
    ARCH=$(uname -m)
    echo -e "${BLUE}系统架构: $ARCH${NC}"
    
    # 检查系统
    if [ -f "/etc/openwrt_release" ]; then
        echo -e "${BLUE}系统类型: OpenWrt${NC}"
        cat /etc/openwrt_release | head -2
    elif [ -f "/etc/os-release" ]; then
        echo -e "${BLUE}系统类型: $(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)${NC}"
    fi
    
    # 检查可用文件
    echo -e "${BLUE}可用的CNS文件:${NC}"
    ls -la cns-* 2>/dev/null || echo "未找到CNS文件"
}

# 创建纯Go版本的数据库文件
create_pure_go_db() {
    echo -e "${YELLOW}📝 创建纯Go数据库文件...${NC}"
    
    cat > db_pure.go << 'EOF'
package main

import (
    "encoding/json"
    "log"
    "os"
    "sync"
    "time"
)

// 纯Go内存数据库
type MemoryDB struct {
    Users           map[string]*User           `json:"users"`
    ConnectionLogs  []ConnectionLog            `json:"connection_logs"`
    TrafficStats    []TrafficStat              `json:"traffic_stats"`
    mutex           sync.RWMutex
}

type User struct {
    UserID    string     `json:"user_id"`
    Username  string     `json:"username"`
    Password  string     `json:"password"`
    Quota     int64      `json:"quota"`
    Upload    int64      `json:"upload"`
    Download  int64      `json:"download"`
    CreatedAt time.Time  `json:"created_at"`
    ExpireAt  *time.Time `json:"expire_at,omitempty"`
    Status    int        `json:"status"`
    WechatID  string     `json:"wechat_id"`
}

type ConnectionLog struct {
    ID             int64     `json:"id"`
    UserID         string    `json:"user_id"`
    ClientIP       string    `json:"client_ip"`
    TargetHost     string    `json:"target_host"`
    ConnectTime    time.Time `json:"connect_time"`
    DisconnectTime *time.Time `json:"disconnect_time,omitempty"`
    Upload         int64     `json:"upload"`
    Download       int64     `json:"download"`
}

type TrafficStat struct {
    ID          int64  `json:"id"`
    UserID      string `json:"user_id"`
    Date        string `json:"date"`
    Upload      int64  `json:"upload"`
    Download    int64  `json:"download"`
    Connections int    `json:"connections"`
}

var (
    memDB      *MemoryDB
    dbFile     = "cns_users.json"
    nextLogID  = int64(1)
    nextStatID = int64(1)
)

// 初始化数据库
func initDatabase() {
    memDB = &MemoryDB{
        Users:          make(map[string]*User),
        ConnectionLogs: make([]ConnectionLog, 0),
        TrafficStats:   make([]TrafficStat, 0),
    }
    
    // 尝试从文件加载数据
    if data, err := os.ReadFile(dbFile); err == nil {
        if err := json.Unmarshal(data, memDB); err != nil {
            log.Printf("加载数据库文件失败: %v", err)
        } else {
            log.Println("数据库文件加载成功")
        }
    }
    
    // 设置下一个ID
    for _, log := range memDB.ConnectionLogs {
        if log.ID >= nextLogID {
            nextLogID = log.ID + 1
        }
    }
    for _, stat := range memDB.TrafficStats {
        if stat.ID >= nextStatID {
            nextStatID = stat.ID + 1
        }
    }
    
    log.Println("数据库初始化完成 (纯Go版本)")
    
    // 定期保存数据
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        for range ticker.C {
            saveDatabase()
        }
    }()
}

// 保存数据库到文件
func saveDatabase() {
    memDB.mutex.RLock()
    data, err := json.MarshalIndent(memDB, "", "  ")
    memDB.mutex.RUnlock()
    
    if err != nil {
        log.Printf("序列化数据库失败: %v", err)
        return
    }
    
    if err := os.WriteFile(dbFile, data, 0644); err != nil {
        log.Printf("保存数据库文件失败: %v", err)
    }
}

// 添加用户
func addUser(userID, username, password string, quota int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user := &User{
        UserID:    userID,
        Username:  username,
        Password:  password,
        Quota:     quota,
        Upload:    0,
        Download:  0,
        CreatedAt: time.Now(),
        Status:    1,
    }
    
    memDB.Users[userID] = user
    log.Printf("添加用户: %s", username)
    return nil
}

// 获取用户流量
func getUserTraffic(userID string) (upload, download, quota int64, err error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return 0, 0, 1073741824, nil
    }
    
    return user.Upload, user.Download, user.Quota, nil
}

// 重置用户流量
func resetUserTraffic(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.Upload = 0
    user.Download = 0
    log.Printf("重置用户流量: %s", userID)
    return nil
}

// 删除用户
func deleteUser(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    delete(memDB.Users, userID)
    log.Printf("删除用户: %s", userID)
    return nil
}

// 绑定微信ID
func bindWechatID(userID, wechatID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.WechatID = wechatID
    log.Printf("绑定微信ID: %s -> %s", userID, wechatID)
    return nil
}

// 根据微信ID获取用户ID
func getUserIDByWechatID(wechatID string) (string, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    for _, user := range memDB.Users {
        if user.WechatID == wechatID {
            return user.UserID, nil
        }
    }
    
    return "", nil
}

// 记录连接日志
func logConnection(userID, clientIP, targetHost string) (int64, error) {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    logID := nextLogID
    nextLogID++
    
    log := ConnectionLog{
        ID:          logID,
        UserID:      userID,
        ClientIP:    clientIP,
        TargetHost:  targetHost,
        ConnectTime: time.Now(),
        Upload:      0,
        Download:    0,
    }
    
    memDB.ConnectionLogs = append(memDB.ConnectionLogs, log)
    return logID, nil
}

// 更新连接日志
func updateConnectionLog(logID int64, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    for i := range memDB.ConnectionLogs {
        if memDB.ConnectionLogs[i].ID == logID {
            now := time.Now()
            memDB.ConnectionLogs[i].DisconnectTime = &now
            memDB.ConnectionLogs[i].Upload = upload
            memDB.ConnectionLogs[i].Download = download
            break
        }
    }
    
    return nil
}

// 其他必需的函数（简化实现）
func getUserConnectionHistory(userID string, limit int) ([]map[string]interface{}, error) {
    return []map[string]interface{}{}, nil
}

func updateDailyTrafficStats(userID string, upload, download int64) error {
    return nil
}

func getUserDailyStats(userID string, days int) ([]map[string]interface{}, error) {
    return []map[string]interface{}{}, nil
}

func updateUserInfo(userID string, quota *int64, status *int) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    if quota != nil {
        user.Quota = *quota
    }
    
    if status != nil {
        user.Status = *status
    }
    
    return nil
}

func setUserExpire(userID string, expireTime time.Time) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.ExpireAt = &expireTime
    return nil
}

func getTotalUserCount() (int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    return len(memDB.Users), nil
}

func getUserList(page, limit int) ([]map[string]interface{}, int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    total := len(memDB.Users)
    var users []map[string]interface{}
    
    for _, user := range memDB.Users {
        userMap := map[string]interface{}{
            "user_id":      user.UserID,
            "username":     user.Username,
            "quota":        user.Quota,
            "upload":       user.Upload,
            "download":     user.Download,
            "created_at":   user.CreatedAt.Format("2006-01-02 15:04:05"),
            "expire_at":    "",
            "status":       user.Status,
            "wechat_id":    user.WechatID,
            "used_percent": float64(user.Upload+user.Download) / float64(user.Quota) * 100,
        }
        
        if user.ExpireAt != nil {
            userMap["expire_at"] = user.ExpireAt.Format("2006-01-02 15:04:05")
        }
        
        users = append(users, userMap)
        if len(users) >= limit {
            break
        }
    }
    
    return users, total, nil
}

func updateUserTrafficInDB(userID string, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.Upload += upload
    user.Download += download
    return nil
}
EOF
    
    echo -e "${GREEN}✅ 纯Go数据库文件创建完成${NC}"
}

# 创建纯Go版本的go.mod
create_pure_go_mod() {
    echo -e "${YELLOW}📝 创建纯Go版本的go.mod...${NC}"
    
    cat > go.mod << 'EOF'
module cns

go 1.18

require (
    github.com/google/uuid v1.3.0
    github.com/gorilla/mux v1.8.0
)
EOF

    cat > go.sum << 'EOF'
github.com/google/uuid v1.3.0 h1:t6JiXgmwXMjEs8VusXIJk2BXHsn+wx8BZdTaoZ5fu7I=
github.com/google/uuid v1.3.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=
EOF
    
    echo -e "${GREEN}✅ 纯Go版本的go.mod创建完成${NC}"
}

# 重新编译纯Go版本
rebuild_pure_go() {
    echo -e "${YELLOW}🔨 重新编译纯Go版本...${NC}"
    
    # 备份原文件
    if [ -f "db.go" ]; then
        mv db.go db_sqlite.go.bak
    fi
    
    # 使用纯Go数据库
    mv db_pure.go db.go
    
    # 设置编译环境
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    # 编译
    if command -v go &> /dev/null; then
        go build -ldflags="-s -w" -o cns-arm64-linux-pure
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 纯Go版本编译成功: cns-arm64-linux-pure${NC}"
            
            # 恢复原文件
            if [ -f "db_sqlite.go.bak" ]; then
                mv db_sqlite.go.bak db.go
            fi
            
            return 0
        else
            echo -e "${RED}❌ 编译失败${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Go编译器未找到${NC}"
        return 1
    fi
}

# 创建启动脚本
create_startup_script() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    cat > start_arm.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (ARM版本)"
echo "================================="

# 检查纯Go版本
if [ -f "cns-arm64-linux-pure" ]; then
    CNS_BINARY="./cns-arm64-linux-pure"
    echo "✅ 使用纯Go版本: cns-arm64-linux-pure"
elif [ -f "cns-arm32-linux-pure" ]; then
    CNS_BINARY="./cns-arm32-linux-pure"
    echo "✅ 使用纯Go版本: cns-arm32-linux-pure"
else
    echo "❌ 未找到纯Go版本的CNS可执行文件"
    echo "请运行: ./fix_arm_device.sh"
    exit 1
fi

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   数据存储: cns_users.json (纯Go版本)"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
EOF
    
    chmod +x start_arm.sh
    echo -e "${GREEN}✅ 启动脚本创建完成: start_arm.sh${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始修复ARM设备CGO问题...${NC}"
    echo ""
    
    # 检查环境
    check_environment
    
    echo ""
    echo -e "${YELLOW}选择修复方案:${NC}"
    echo "1) 使用现有的纯Go版本 (如果存在)"
    echo "2) 重新编译纯Go版本"
    echo "3) 仅创建启动脚本"
    echo ""
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            if [ -f "cns-arm64-linux-pure" ] || [ -f "cns-arm32-linux-pure" ]; then
                echo -e "${GREEN}✅ 找到纯Go版本${NC}"
                create_startup_script
            else
                echo -e "${YELLOW}⚠️  未找到纯Go版本，将重新编译${NC}"
                create_pure_go_db
                create_pure_go_mod
                rebuild_pure_go
                create_startup_script
            fi
            ;;
        2)
            create_pure_go_db
            create_pure_go_mod
            rebuild_pure_go
            create_startup_script
            ;;
        3)
            create_startup_script
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            exit 1
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}🎉 修复完成！${NC}"
    echo ""
    echo -e "${BLUE}🚀 现在可以使用以下命令启动:${NC}"
    echo "  ./start_arm.sh"
    echo ""
    echo -e "${BLUE}或手动启动:${NC}"
    echo "  ./cns-arm64-linux-pure -json config_arm.json"
    echo ""
    echo -e "${YELLOW}💡 说明:${NC}"
    echo "  - 纯Go版本无需CGO依赖"
    echo "  - 数据存储在 cns_users.json 文件中"
    echo "  - 支持所有原有功能"
}

# 运行主函数
main "$@"
