#!/usr/bin/env python3
"""
CNS 连接和用户信息更新诊断脚本
用于检查用户连接后信息是否正确更新
"""

import requests
import json
import time
import base64
import socket
import threading
from datetime import datetime

class CNSDebugger:
    def __init__(self, api_base_url="http://localhost:8080", proxy_host="localhost", proxy_port=1254):
        self.api_base_url = api_base_url
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        self.session = requests.Session()
    
    def check_service_status(self):
        """检查服务状态"""
        print("🔍 检查服务状态...")
        
        try:
            # 检查API服务
            response = self.session.get(f"{self.api_base_url}/api/system/status", timeout=5)
            response.raise_for_status()
            status = response.json()
            print(f"✅ API服务正常运行")
            print(f"   在线用户: {status['online_users']}")
            print(f"   总用户数: {status['total_users']}")
            print(f"   服务器时间: {status['server_time']}")
            return True
        except Exception as e:
            print(f"❌ API服务异常: {e}")
            return False
    
    def check_proxy_port(self):
        """检查代理端口是否可用"""
        print(f"\n🔍 检查代理端口 {self.proxy_host}:{self.proxy_port}...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.proxy_host, self.proxy_port))
            sock.close()
            
            if result == 0:
                print(f"✅ 代理端口 {self.proxy_port} 可访问")
                return True
            else:
                print(f"❌ 代理端口 {self.proxy_port} 无法访问")
                return False
        except Exception as e:
            print(f"❌ 检查代理端口失败: {e}")
            return False
    
    def create_test_user(self):
        """创建测试用户"""
        print(f"\n👤 创建测试用户...")
        
        username = f"debug_user_{int(time.time())}"
        password = "debug123"
        quota = 1024 * 1024 * 1024  # 1GB
        
        try:
            data = {
                "username": username,
                "password": password,
                "quota": quota
            }
            
            response = self.session.post(f"{self.api_base_url}/api/user/add", json=data)
            response.raise_for_status()
            result = response.json()
            
            user_id = result['user_id']
            print(f"✅ 测试用户创建成功")
            print(f"   用户名: {username}")
            print(f"   密码: {password}")
            print(f"   用户ID: {user_id}")
            print(f"   配额: {quota / 1024 / 1024} MB")
            
            return {
                'user_id': user_id,
                'username': username,
                'password': password,
                'quota': quota
            }
        except Exception as e:
            print(f"❌ 创建测试用户失败: {e}")
            return None
    
    def get_user_traffic_before(self, user_id):
        """获取连接前的用户流量信息"""
        print(f"\n📊 获取连接前的用户流量信息...")
        
        try:
            # 获取用户基本信息
            response = self.session.get(f"{self.api_base_url}/api/user/info/{user_id}")
            response.raise_for_status()
            user_info = response.json()
            
            # 获取流量信息
            response = self.session.get(f"{self.api_base_url}/api/user/traffic?user_id={user_id}")
            response.raise_for_status()
            traffic_info = response.json()
            
            # 获取实时统计
            response = self.session.get(f"{self.api_base_url}/api/stats/realtime/{user_id}")
            response.raise_for_status()
            realtime_info = response.json()
            
            print(f"✅ 连接前用户信息:")
            print(f"   数据库上传: {user_info['upload']} bytes")
            print(f"   数据库下载: {user_info['download']} bytes")
            print(f"   实时上传: {realtime_info['realtime_upload']} bytes")
            print(f"   实时下载: {realtime_info['realtime_download']} bytes")
            print(f"   总上传: {realtime_info['total_upload']} bytes")
            print(f"   总下载: {realtime_info['total_download']} bytes")
            
            return {
                'db_upload': user_info['upload'],
                'db_download': user_info['download'],
                'realtime_upload': realtime_info['realtime_upload'],
                'realtime_download': realtime_info['realtime_download'],
                'total_upload': realtime_info['total_upload'],
                'total_download': realtime_info['total_download']
            }
        except Exception as e:
            print(f"❌ 获取用户流量信息失败: {e}")
            return None
    
    def test_proxy_connection_with_auth(self, user_info):
        """测试带认证的代理连接"""
        print(f"\n🔗 测试带认证的代理连接...")
        
        username = user_info['username']
        password = user_info['password']
        user_id = user_info['user_id']
        
        # 方法1: 使用Basic认证
        print(f"📝 方法1: 使用Basic认证")
        try:
            auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
            
            # 创建带认证的HTTP请求
            headers = {
                'Authorization': f'Basic {auth_string}',
                'User-ID': user_id,
                'Host': 'httpbin.org'
            }
            
            # 使用代理发送请求
            proxies = {
                'http': f'http://{self.proxy_host}:{self.proxy_port}',
                'https': f'http://{self.proxy_host}:{self.proxy_port}'
            }
            
            print(f"   发送请求到 httpbin.org/get...")
            response = requests.get('http://httpbin.org/get', 
                                  headers=headers, 
                                  proxies=proxies, 
                                  timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Basic认证代理连接成功")
                print(f"   响应状态: {response.status_code}")
                print(f"   响应大小: {len(response.content)} bytes")
                return True
            else:
                print(f"❌ Basic认证代理连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Basic认证代理连接异常: {e}")
        
        # 方法2: 使用CONNECT方法
        print(f"\n📝 方法2: 使用CONNECT方法")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((self.proxy_host, self.proxy_port))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT httpbin.org:80 HTTP/1.1\r\n"
            connect_request += f"Host: httpbin.org:80\r\n"
            connect_request += f"Authorization: Basic {auth_string}\r\n"
            connect_request += f"User-ID: {user_id}\r\n"
            connect_request += f"\r\n"
            
            sock.send(connect_request.encode())
            response = sock.recv(1024).decode()
            
            if "200 Connection established" in response:
                print(f"✅ CONNECT方法连接成功")
                
                # 发送HTTP请求
                http_request = "GET /get HTTP/1.1\r\n"
                http_request += "Host: httpbin.org\r\n"
                http_request += "Connection: close\r\n"
                http_request += "\r\n"
                
                sock.send(http_request.encode())
                http_response = sock.recv(4096)
                
                print(f"   HTTP响应大小: {len(http_response)} bytes")
                sock.close()
                return True
            else:
                print(f"❌ CONNECT方法连接失败: {response}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"❌ CONNECT方法连接异常: {e}")
            return False
    
    def get_user_traffic_after(self, user_id, before_stats):
        """获取连接后的用户流量信息并比较"""
        print(f"\n📊 获取连接后的用户流量信息...")
        
        # 等待一下让流量统计更新
        time.sleep(2)
        
        try:
            # 获取用户基本信息
            response = self.session.get(f"{self.api_base_url}/api/user/info/{user_id}")
            response.raise_for_status()
            user_info = response.json()
            
            # 获取流量信息
            response = self.session.get(f"{self.api_base_url}/api/user/traffic?user_id={user_id}")
            response.raise_for_status()
            traffic_info = response.json()
            
            # 获取实时统计
            response = self.session.get(f"{self.api_base_url}/api/stats/realtime/{user_id}")
            response.raise_for_status()
            realtime_info = response.json()
            
            print(f"✅ 连接后用户信息:")
            print(f"   数据库上传: {user_info['upload']} bytes")
            print(f"   数据库下载: {user_info['download']} bytes")
            print(f"   实时上传: {realtime_info['realtime_upload']} bytes")
            print(f"   实时下载: {realtime_info['realtime_download']} bytes")
            print(f"   总上传: {realtime_info['total_upload']} bytes")
            print(f"   总下载: {realtime_info['total_download']} bytes")
            
            # 计算变化
            print(f"\n📈 流量变化:")
            db_upload_change = user_info['upload'] - before_stats['db_upload']
            db_download_change = user_info['download'] - before_stats['db_download']
            realtime_upload_change = realtime_info['realtime_upload'] - before_stats['realtime_upload']
            realtime_download_change = realtime_info['realtime_download'] - before_stats['realtime_download']
            total_upload_change = realtime_info['total_upload'] - before_stats['total_upload']
            total_download_change = realtime_info['total_download'] - before_stats['total_download']
            
            print(f"   数据库上传变化: +{db_upload_change} bytes")
            print(f"   数据库下载变化: +{db_download_change} bytes")
            print(f"   实时上传变化: +{realtime_upload_change} bytes")
            print(f"   实时下载变化: +{realtime_download_change} bytes")
            print(f"   总上传变化: +{total_upload_change} bytes")
            print(f"   总下载变化: +{total_download_change} bytes")
            
            # 判断是否有流量更新
            has_traffic_update = (
                db_upload_change > 0 or db_download_change > 0 or
                realtime_upload_change > 0 or realtime_download_change > 0 or
                total_upload_change > 0 or total_download_change > 0
            )
            
            if has_traffic_update:
                print(f"✅ 检测到流量更新")
                return True
            else:
                print(f"❌ 未检测到流量更新")
                return False
                
        except Exception as e:
            print(f"❌ 获取连接后流量信息失败: {e}")
            return False
    
    def cleanup_test_user(self, user_id):
        """清理测试用户"""
        print(f"\n🗑️ 清理测试用户...")
        
        try:
            response = self.session.delete(f"{self.api_base_url}/api/user/delete?user_id={user_id}")
            response.raise_for_status()
            result = response.json()
            
            if result.get('success'):
                print(f"✅ 测试用户清理成功")
            else:
                print(f"❌ 测试用户清理失败")
        except Exception as e:
            print(f"❌ 清理测试用户异常: {e}")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🚀 CNS 连接和用户信息更新诊断")
        print("=" * 50)
        
        # 1. 检查服务状态
        if not self.check_service_status():
            print("\n❌ 服务状态检查失败，请确保CNS服务正在运行")
            return False
        
        # 2. 检查代理端口
        if not self.check_proxy_port():
            print("\n❌ 代理端口检查失败，请确保代理服务正在运行")
            return False
        
        # 3. 创建测试用户
        user_info = self.create_test_user()
        if not user_info:
            print("\n❌ 测试用户创建失败")
            return False
        
        try:
            # 4. 获取连接前的流量信息
            before_stats = self.get_user_traffic_before(user_info['user_id'])
            if not before_stats:
                print("\n❌ 获取连接前流量信息失败")
                return False
            
            # 5. 测试代理连接
            connection_success = self.test_proxy_connection_with_auth(user_info)
            
            # 6. 获取连接后的流量信息并比较
            traffic_updated = self.get_user_traffic_after(user_info['user_id'], before_stats)
            
            # 7. 分析结果
            print(f"\n📋 诊断结果:")
            print(f"   代理连接: {'✅ 成功' if connection_success else '❌ 失败'}")
            print(f"   流量更新: {'✅ 正常' if traffic_updated else '❌ 异常'}")
            
            if connection_success and traffic_updated:
                print(f"\n🎉 诊断结果: 系统工作正常")
                return True
            elif connection_success and not traffic_updated:
                print(f"\n⚠️ 诊断结果: 连接成功但流量未更新")
                print(f"   可能原因:")
                print(f"   1. 流量控制未启用 (Enable_traffic_control=false)")
                print(f"   2. 用户认证信息未正确传递")
                print(f"   3. 流量统计保存间隔较长")
                print(f"   4. 数据库写入权限问题")
                return False
            else:
                print(f"\n❌ 诊断结果: 连接失败")
                print(f"   可能原因:")
                print(f"   1. 用户认证配置错误")
                print(f"   2. 代理服务配置问题")
                print(f"   3. 网络连接问题")
                return False
        
        finally:
            # 8. 清理测试用户
            self.cleanup_test_user(user_info['user_id'])

def main():
    debugger = CNSDebugger()
    debugger.run_full_diagnosis()

if __name__ == "__main__":
    main()
