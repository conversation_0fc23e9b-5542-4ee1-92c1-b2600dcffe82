#!/usr/bin/env python3
"""
CNS 代理连接测试
使用CNS自定义协议格式
"""

import requests
import json
import time
import base64
import socket

def create_test_user():
    """创建测试用户"""
    print("👤 创建测试用户...")
    
    username = f"cns_test_{int(time.time())}"
    password = "test123"
    quota = 100 * 1024 * 1024  # 100MB
    
    try:
        data = {
            "username": username,
            "password": password,
            "quota": quota
        }
        
        response = requests.post("http://localhost:8080/api/user/add", json=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        
        user_id = result['user_id']
        print(f"✅ 测试用户创建成功: {username} (ID: {user_id})")
        
        return {
            'user_id': user_id,
            'username': username,
            'password': password,
            'quota': quota
        }
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        return None

def get_user_stats(user_id):
    """获取用户统计信息"""
    try:
        response = requests.get(f"http://localhost:8080/api/user/info/{user_id}", timeout=10)
        if response.status_code != 200:
            return None
        
        user_info = response.json()
        
        try:
            response = requests.get(f"http://localhost:8080/api/stats/realtime/{user_id}", timeout=10)
            if response.status_code == 200:
                realtime_info = response.json()
            else:
                realtime_info = {
                    'realtime_upload': 0,
                    'realtime_download': 0,
                    'total_upload': user_info['upload'],
                    'total_download': user_info['download']
                }
        except:
            realtime_info = {
                'realtime_upload': 0,
                'realtime_download': 0,
                'total_upload': user_info['upload'],
                'total_download': user_info['download']
            }
        
        return {
            'db_upload': user_info['upload'],
            'db_download': user_info['download'],
            'realtime_upload': realtime_info['realtime_upload'],
            'realtime_download': realtime_info['realtime_download'],
            'total_upload': realtime_info['total_upload'],
            'total_download': realtime_info['total_download']
        }
    except Exception as e:
        print(f"❌ 获取用户统计失败: {e}")
        return None

def test_cns_proxy_connection(username, password, user_id):
    """测试CNS代理连接 - 使用CNS自定义协议"""
    print(f"\n🔗 测试CNS代理连接 (自定义协议)...")
    
    try:
        # 连接CNS代理
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(15)
        sock.connect(('localhost', 1254))
        
        # CNS自定义协议格式
        # 格式: proxy_key + 目标地址 + 认证信息
        proxy_key = "Meng"  # 从config.json中获取
        target_host = "httpbin.org:80"
        
        # 创建认证字符串
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        
        # 构建CNS协议请求
        # 格式: proxy_key + target_host + \r\n + 认证信息
        cns_request = f"{proxy_key}{target_host}\r\n"
        cns_request += f"Authorization: Basic {auth_string}\r\n"
        cns_request += f"User-ID: {user_id}\r\n"
        cns_request += f"\r\n"
        
        print(f"📤 发送CNS协议请求:")
        print(f"   代理密钥: {proxy_key}")
        print(f"   目标地址: {target_host}")
        print(f"   用户ID: {user_id}")
        
        sock.send(cns_request.encode())
        
        # 等待连接建立确认
        time.sleep(1)
        
        # 发送HTTP请求
        http_request = "GET /json HTTP/1.1\r\n"
        http_request += "Host: httpbin.org\r\n"
        http_request += "User-Agent: CNS-Test-Client\r\n"
        http_request += "Accept: application/json\r\n"
        http_request += "Connection: close\r\n"
        http_request += "\r\n"
        
        print(f"📤 发送HTTP请求...")
        sock.send(http_request.encode())
        
        # 接收响应
        http_response = b""
        total_received = 0
        while True:
            try:
                data = sock.recv(4096)
                if not data:
                    break
                http_response += data
                total_received += len(data)
                print(f"📥 接收数据: {len(data)} bytes (总计: {total_received} bytes)")
            except socket.timeout:
                break
        
        sock.close()
        
        print(f"📊 总接收数据: {total_received} bytes")
        
        # 检查响应内容
        if total_received > 0 and (b"httpbin.org" in http_response or b"origin" in http_response):
            print(f"✅ CNS代理连接成功，收到有效响应")
            return True, total_received
        elif total_received > 0:
            print(f"⚠️ CNS代理连接成功，但响应可能有问题")
            print(f"   响应前100字节: {http_response[:100]}")
            return True, total_received
        else:
            print(f"❌ CNS代理连接失败，未收到数据")
            return False, 0
            
    except Exception as e:
        print(f"❌ CNS代理连接异常: {e}")
        return False, 0

def test_http_connect_method(username, password, user_id):
    """测试HTTP CONNECT方法 - 标准HTTP代理协议"""
    print(f"\n🔗 测试HTTP CONNECT方法 (标准协议)...")
    
    try:
        # 连接代理
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(15)
        sock.connect(('localhost', 1254))
        
        # 创建认证字符串
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        
        # 发送标准HTTP CONNECT请求
        connect_request = f"CONNECT httpbin.org:80 HTTP/1.1\r\n"
        connect_request += f"Host: httpbin.org:80\r\n"
        connect_request += f"Authorization: Basic {auth_string}\r\n"
        connect_request += f"User-ID: {user_id}\r\n"
        connect_request += f"Proxy-Connection: keep-alive\r\n"
        connect_request += f"\r\n"
        
        print(f"📤 发送HTTP CONNECT请求...")
        sock.send(connect_request.encode())
        
        # 接收代理响应
        response = sock.recv(1024).decode()
        print(f"📥 代理响应: {response.strip()}")
        
        if "200 Connection established" in response:
            print(f"✅ HTTP CONNECT连接建立成功")
            
            # 发送HTTP请求
            http_request = "GET /json HTTP/1.1\r\n"
            http_request += "Host: httpbin.org\r\n"
            http_request += "User-Agent: CNS-Test-Client\r\n"
            http_request += "Accept: application/json\r\n"
            http_request += "Connection: close\r\n"
            http_request += "\r\n"
            
            print(f"📤 发送HTTP请求...")
            sock.send(http_request.encode())
            
            # 接收HTTP响应
            http_response = b""
            total_received = 0
            while True:
                try:
                    data = sock.recv(4096)
                    if not data:
                        break
                    http_response += data
                    total_received += len(data)
                    print(f"📥 接收数据: {len(data)} bytes (总计: {total_received} bytes)")
                except socket.timeout:
                    break
            
            sock.close()
            
            print(f"📊 总接收数据: {total_received} bytes")
            
            if total_received > 0:
                print(f"✅ HTTP请求成功")
                return True, total_received
            else:
                print(f"❌ HTTP请求失败，未收到数据")
                return False, 0
        else:
            print(f"❌ HTTP CONNECT连接失败")
            sock.close()
            return False, 0
            
    except Exception as e:
        print(f"❌ HTTP CONNECT连接异常: {e}")
        return False, 0

def cleanup_user(user_id):
    """清理测试用户"""
    print(f"\n🗑️ 清理测试用户...")
    
    try:
        response = requests.delete(f"http://localhost:8080/api/user/delete?user_id={user_id}", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 测试用户清理成功")
            else:
                print(f"❌ 测试用户清理失败")
        else:
            print(f"❌ 清理请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 清理测试用户异常: {e}")

def main():
    print("🚀 CNS 代理连接测试")
    print("=" * 50)
    
    # 1. 创建测试用户
    user_info = create_test_user()
    if not user_info:
        return False
    
    try:
        # 2. 获取初始统计
        print(f"\n📊 获取初始流量统计...")
        before_stats = get_user_stats(user_info['user_id'])
        if not before_stats:
            print(f"❌ 无法获取初始统计")
            return False
        
        print(f"📈 初始统计:")
        print(f"   数据库上传: {before_stats['db_upload']} bytes")
        print(f"   数据库下载: {before_stats['db_download']} bytes")
        
        # 3. 测试CNS自定义协议
        print(f"\n" + "="*50)
        cns_success, cns_data = test_cns_proxy_connection(
            user_info['username'], 
            user_info['password'],
            user_info['user_id']
        )
        
        # 4. 测试HTTP CONNECT方法
        print(f"\n" + "="*50)
        http_success, http_data = test_http_connect_method(
            user_info['username'], 
            user_info['password'],
            user_info['user_id']
        )
        
        # 5. 等待流量统计更新
        print(f"\n⏳ 等待15秒让流量统计更新...")
        time.sleep(15)
        
        # 6. 检查流量更新
        print(f"\n📊 检查流量更新...")
        after_stats = get_user_stats(user_info['user_id'])
        if not after_stats:
            print(f"❌ 无法获取更新后统计")
            return False
        
        print(f"📈 更新后统计:")
        print(f"   数据库上传: {after_stats['db_upload']} bytes")
        print(f"   数据库下载: {after_stats['db_download']} bytes")
        
        # 7. 计算变化
        print(f"\n📊 流量变化:")
        db_upload_change = after_stats['db_upload'] - before_stats['db_upload']
        db_download_change = after_stats['db_download'] - before_stats['db_download']
        
        print(f"   数据库上传变化: +{db_upload_change} bytes")
        print(f"   数据库下载变化: +{db_download_change} bytes")
        
        # 8. 分析结果
        has_traffic_update = db_upload_change > 0 or db_download_change > 0
        
        print(f"\n📋 测试结果:")
        print(f"   CNS协议连接: {'✅ 成功' if cns_success else '❌ 失败'}")
        print(f"   HTTP CONNECT连接: {'✅ 成功' if http_success else '❌ 失败'}")
        print(f"   流量统计更新: {'✅ 正常' if has_traffic_update else '❌ 异常'}")
        
        if (cns_success or http_success) and has_traffic_update:
            print(f"\n🎉 测试完全成功: 连接和流量统计都正常工作")
            return True
        elif (cns_success or http_success) and not has_traffic_update:
            print(f"\n⚠️ 部分成功: 连接正常但流量统计未更新")
            return False
        else:
            print(f"\n❌ 测试失败: 连接有问题")
            return False
    
    finally:
        # 9. 清理测试用户
        cleanup_user(user_info['user_id'])

if __name__ == "__main__":
    main()
