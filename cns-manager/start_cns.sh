#!/bin/bash
echo "🚀 启动 CNS 流量控制系统"

# 自动检测架构
ARCH=$(uname -m)
case $ARCH in
    "x86_64")
        if [ -f "cns-amd64-linux" ]; then
            CNS_BINARY="./cns-amd64-linux"
            echo "使用 AMD64 版本 (支持SQLite)"
        else
            echo "❌ 未找到AMD64可执行文件"
            exit 1
        fi
        ;;
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux-pure" ]; then
            CNS_BINARY="./cns-arm64-linux-pure"
            echo "使用 ARM64 纯Go版本"
        else
            echo "❌ 未找到ARM64可执行文件"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux-pure" ]; then
            CNS_BINARY="./cns-arm32-linux-pure"
            echo "使用 ARM32 纯Go版本"
        else
            echo "❌ 未找到ARM32可执行文件"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        exit 1
        ;;
esac

chmod +x $CNS_BINARY

CONFIG_FILE="config_arm.json"
if [ ! -f "$CONFIG_FILE" ]; then
    CONFIG_FILE="config.json"
fi

echo "配置文件: $CONFIG_FILE"
echo "代理服务: localhost:1254"
echo "API服务: http://localhost:8080"
echo "Web管理: http://localhost:8081"
echo ""

$CNS_BINARY -json=$CONFIG_FILE
