#!/usr/bin/env python3
"""
测试数据库修复是否有效
"""

import requests
import json
import time

def test_user_creation_and_retrieval():
    """测试用户创建和检索"""
    print("🧪 测试数据库修复...")
    
    # 创建测试用户
    username = f"dbtest_{int(time.time())}"
    password = "test123"
    quota = 50 * 1024 * 1024  # 50MB
    
    try:
        # 1. 创建用户
        print(f"1️⃣ 创建用户: {username}")
        data = {
            "username": username,
            "password": password,
            "quota": quota
        }
        
        response = requests.post("http://localhost:8080/api/user/add", json=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        user_id = result['user_id']
        print(f"   ✅ 用户创建成功，ID: {user_id}")
        
        # 2. 立即尝试获取用户信息
        print(f"2️⃣ 获取用户信息...")
        response = requests.get(f"http://localhost:8080/api/user/info/{user_id}", timeout=10)
        
        if response.status_code == 200:
            user_info = response.json()
            print(f"   ✅ 用户信息获取成功:")
            print(f"      用户ID: {user_info['user_id']}")
            print(f"      用户名: {user_info['username']}")
            print(f"      配额: {user_info['quota']} bytes")
            print(f"      上传: {user_info['upload']} bytes")
            print(f"      下载: {user_info['download']} bytes")
            print(f"      状态: {user_info['status']}")
            print(f"      微信ID: {user_info.get('wechat_id', 'None')}")
            
            # 3. 清理用户
            print(f"3️⃣ 清理测试用户...")
            response = requests.delete(f"http://localhost:8080/api/user/delete?user_id={user_id}", timeout=10)
            if response.status_code == 200:
                print(f"   ✅ 用户清理成功")
            else:
                print(f"   ⚠️ 用户清理失败: HTTP {response.status_code}")
            
            print(f"\n🎉 数据库修复测试通过!")
            return True
        else:
            print(f"   ❌ 用户信息获取失败: HTTP {response.status_code}")
            print(f"      错误信息: {response.text}")
            
            # 仍然尝试清理用户
            try:
                requests.delete(f"http://localhost:8080/api/user/delete?user_id={user_id}", timeout=5)
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("🔧 CNS 数据库修复验证")
    print("=" * 30)
    
    # 检查API是否可用
    try:
        response = requests.get("http://localhost:8080/api/system/status", timeout=5)
        if response.status_code == 200:
            print("✅ API服务可用")
        else:
            print("❌ API服务不可用")
            return False
    except Exception as e:
        print(f"❌ 无法连接API服务: {e}")
        return False
    
    # 运行测试
    success = test_user_creation_and_retrieval()
    
    if success:
        print(f"\n✅ 数据库修复验证成功!")
        print(f"   现在可以正常创建和获取用户信息")
        print(f"   wechat_id NULL值问题已解决")
    else:
        print(f"\n❌ 数据库修复验证失败")
        print(f"   需要重新编译程序以应用修复")
    
    return success

if __name__ == "__main__":
    main()
