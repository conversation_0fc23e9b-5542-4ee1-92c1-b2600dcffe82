#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (ARM纯Go版本)"
echo "====================================="

# 自动检测架构
ARCH=$(uname -m)
case $ARCH in
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux-pure" ]; then
            CNS_BINARY="./cns-arm64-linux-pure"
            echo "✅ 使用 ARM64 纯Go版本"
        else
            echo "❌ 未找到ARM64纯Go版本: cns-arm64-linux-pure"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux-pure" ]; then
            CNS_BINARY="./cns-arm32-linux-pure"
            echo "✅ 使用 ARM32 纯Go版本"
        else
            echo "❌ 未找到ARM32纯Go版本: cns-arm32-linux-pure"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        echo "支持的架构: aarch64, arm64, armv7l, arm"
        exit 1
        ;;
esac

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件 (config_arm.json 或 config.json)"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   数据存储: cns_users.json (纯Go版本)"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 特点:"
echo "   - 无CGO依赖，适合ARM设备"
echo "   - 数据存储在JSON文件中"
echo "   - 自动定期保存数据"
echo "   - 简化版本，核心功能完整"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
