#!/bin/bash
echo "🚀 启动 CNS 流量控制系统"
echo "========================"

# 检测架构并选择对应的可执行文件
ARCH=$(uname -m)
case $ARCH in
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux" ]; then
            CNS_BINARY="./cns-arm64-linux"
        else
            echo "❌ 未找到ARM64可执行文件"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux" ]; then
            CNS_BINARY="./cns-arm32-linux"
        else
            echo "❌ 未找到ARM32可执行文件"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        exit 1
        ;;
esac

chmod +x $CNS_BINARY
echo "使用可执行文件: $CNS_BINARY"
echo "配置文件: config_arm.json"
echo ""
echo "服务地址:"
echo "  代理服务: localhost:1254"
echo "  API服务: http://localhost:8080"
echo "  Web管理: http://localhost:8081"
echo ""

$CNS_BINARY -json=config_arm.json
