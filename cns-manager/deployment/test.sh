#!/bin/bash
echo "🧪 CNS 快速测试"

# 检测架构
ARCH=$(uname -m)
case $ARCH in
    "x86_64")
        BINARY="./cns-amd64-linux"
        ;;
    "aarch64"|"arm64")
        BINARY="./cns-arm64-linux-pure"
        ;;
    "armv7l"|"arm")
        BINARY="./cns-arm32-linux-pure"
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        exit 1
        ;;
esac

if [ ! -f "$BINARY" ]; then
    echo "❌ 可执行文件不存在: $BINARY"
    exit 1
fi

chmod +x $BINARY
echo "✅ 使用二进制文件: $BINARY"

# 测试启动
echo "🚀 测试启动（10秒）..."
timeout 10 $BINARY -json=config_arm.json &
PID=$!

sleep 3

# 测试API
if curl -s http://localhost:8080/api/system/status; then
    echo ""
    echo "✅ 测试成功！"
else
    echo "⚠️  API测试失败，但程序可能正常运行"
fi

kill $PID 2>/dev/null
echo "🏁 测试完成"
