# CNS 流量控制系统部署包

## 文件说明
- `cns-amd64-linux` - AMD64版本（支持SQLite数据库）
- `cns-arm64-linux-pure` - ARM64版本（纯Go内存数据库）
- `cns-arm32-linux-pure` - ARM32版本（纯Go内存数据库）
- `config_arm.json` - 配置文件
- `start_cns.sh` - 启动脚本

## 使用方法

### 自动启动（推荐）
```bash
./start_cns.sh
```

### 手动启动
```bash
# AMD64系统
./cns-amd64-linux -json=config_arm.json

# ARM64系统
./cns-arm64-linux-pure -json=config_arm.json

# ARM32系统
./cns-arm32-linux-pure -json=config_arm.json
```

## 访问地址
- Web管理界面: http://设备IP:8081
- API服务: http://设备IP:8080
- 代理服务: 设备IP:1254

## 数据存储
- AMD64版本: SQLite数据库文件 `cns_users.db`
- ARM版本: JSON文件 `cns_users.json`

## 注意事项
1. 确保防火墙允许相应端口
2. 建议使用非root用户运行
3. ARM版本数据存储在内存中，定期保存到JSON文件
