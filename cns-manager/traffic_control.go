package main

import (
    "database/sql"
    "fmt"
    "log"
    "net"
    "sync"
    "time"
)

// 流量统计结构
type TrafficStats struct {
    UserID   string
    Upload   int64
    Download int64
    mutex    sync.RWMutex
}

// 全局流量统计映射
var (
    userTrafficStats = make(map[string]*TrafficStats)
    trafficMutex     sync.RWMutex
)

// 用户认证信息
type UserAuth struct {
    UserID    string
    Username  string
    Password  string
    Quota     int64
    Upload    int64
    Download  int64
    ExpireAt  *time.Time
    Status    int
    WechatID  string
}

// 验证用户身份并检查流量配额
func authenticateUser(username, password string) (*UserAuth, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return nil, err
    }
    defer db.Close()

    var user UserAuth
    var expireAt sql.NullTime
    
    query := `SELECT user_id, username, password, quota, upload, download, expire_at, status, wechat_id 
              FROM users WHERE username = ? AND password = ? AND status = 1`
    
    err = db.QueryRow(query, username, password).Scan(
        &user.UserID, &user.Username, &user.Password, &user.Quota,
        &user.Upload, &user.Download, &expireAt, &user.Status, &user.WechatID)
    
    if err != nil {
        return nil, fmt.Errorf("用户认证失败: %v", err)
    }
    
    if expireAt.Valid {
        user.ExpireAt = &expireAt.Time
        if time.Now().After(*user.ExpireAt) {
            return nil, fmt.Errorf("用户已过期")
        }
    }
    
    // 检查流量配额
    if user.Upload+user.Download >= user.Quota {
        return nil, fmt.Errorf("流量配额已用完")
    }
    
    return &user, nil
}

// 根据用户ID获取用户信息
func getUserByID(userID string) (*UserAuth, error) {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return nil, err
    }
    defer db.Close()

    var user UserAuth
    var expireAt sql.NullTime
    
    query := `SELECT user_id, username, password, quota, upload, download, expire_at, status, wechat_id 
              FROM users WHERE user_id = ?`
    
    err = db.QueryRow(query, userID).Scan(
        &user.UserID, &user.Username, &user.Password, &user.Quota,
        &user.Upload, &user.Download, &expireAt, &user.Status, &user.WechatID)
    
    if err != nil {
        return nil, err
    }
    
    if expireAt.Valid {
        user.ExpireAt = &expireAt.Time
    }
    
    return &user, nil
}

// 检查用户是否有足够的流量配额
func checkUserQuota(userID string) bool {
    trafficMutex.RLock()
    stats, exists := userTrafficStats[userID]
    trafficMutex.RUnlock()
    
    if !exists {
        return true // 如果没有流量记录，允许连接
    }
    
    user, err := getUserByID(userID)
    if err != nil {
        log.Printf("获取用户信息失败: %v", err)
        return false
    }
    
    stats.mutex.RLock()
    totalUsed := user.Upload + user.Download + stats.Upload + stats.Download
    stats.mutex.RUnlock()
    
    return totalUsed < user.Quota
}

// 记录流量使用
func recordTraffic(userID string, upload, download int64) {
    trafficMutex.Lock()
    stats, exists := userTrafficStats[userID]
    if !exists {
        stats = &TrafficStats{
            UserID: userID,
        }
        userTrafficStats[userID] = stats
    }
    trafficMutex.Unlock()
    
    stats.mutex.Lock()
    stats.Upload += upload
    stats.Download += download
    stats.mutex.Unlock()
}

// 获取用户实时流量统计
func getUserTrafficStats(userID string) (upload, download int64) {
    trafficMutex.RLock()
    stats, exists := userTrafficStats[userID]
    trafficMutex.RUnlock()
    
    if !exists {
        return 0, 0
    }
    
    stats.mutex.RLock()
    upload = stats.Upload
    download = stats.Download
    stats.mutex.RUnlock()
    
    return upload, download
}

// 定期保存流量统计到数据库
func saveTrafficToDB() {
    ticker := time.NewTicker(30 * time.Second) // 每30秒保存一次
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            trafficMutex.RLock()
            statsToSave := make(map[string]*TrafficStats)
            for userID, stats := range userTrafficStats {
                statsToSave[userID] = &TrafficStats{
                    UserID:   stats.UserID,
                    Upload:   stats.Upload,
                    Download: stats.Download,
                }
            }
            trafficMutex.RUnlock()
            
            for userID, stats := range statsToSave {
                if stats.Upload > 0 || stats.Download > 0 {
                    updateUserTrafficInDB(userID, stats.Upload, stats.Download)
                    
                    // 重置内存中的统计
                    trafficMutex.Lock()
                    if memStats, exists := userTrafficStats[userID]; exists {
                        memStats.mutex.Lock()
                        memStats.Upload = 0
                        memStats.Download = 0
                        memStats.mutex.Unlock()
                    }
                    trafficMutex.Unlock()
                }
            }
        }
    }
}

// 更新数据库中的用户流量
func updateUserTrafficInDB(userID string, upload, download int64) error {
    db, err := sql.Open("sqlite3", "cns_users.db")
    if err != nil {
        return err
    }
    defer db.Close()
    
    _, err = db.Exec("UPDATE users SET upload = upload + ?, download = download + ? WHERE user_id = ?",
                     upload, download, userID)
    if err != nil {
        log.Printf("更新用户流量失败: %v", err)
    }
    
    return err
}

// 包装连接以进行流量统计
type TrafficConn struct {
    net.Conn
    userID   string
    upload   int64
    download int64
}

func NewTrafficConn(conn net.Conn, userID string) *TrafficConn {
    return &TrafficConn{
        Conn:   conn,
        userID: userID,
    }
}

func (tc *TrafficConn) Read(b []byte) (n int, err error) {
    n, err = tc.Conn.Read(b)
    if n > 0 {
        tc.download += int64(n)
        recordTraffic(tc.userID, 0, int64(n))
    }
    return n, err
}

func (tc *TrafficConn) Write(b []byte) (n int, err error) {
    n, err = tc.Conn.Write(b)
    if n > 0 {
        tc.upload += int64(n)
        recordTraffic(tc.userID, int64(n), 0)
    }
    return n, err
}

func (tc *TrafficConn) Close() error {
    // 最终记录流量
    if tc.upload > 0 || tc.download > 0 {
        recordTraffic(tc.userID, tc.upload, tc.download)
    }
    return tc.Conn.Close()
}
