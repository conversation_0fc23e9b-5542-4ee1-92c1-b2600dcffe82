#!/bin/bash

# CNS WSL ARM 编译脚本

echo "🚀 CNS WSL ARM 编译脚本"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查WSL环境
check_wsl() {
    echo -e "${YELLOW}🔍 检查WSL环境...${NC}"
    
    if grep -qi microsoft /proc/version; then
        echo -e "${GREEN}✅ 检测到WSL环境${NC}"
        WSL_VERSION=$(grep -i microsoft /proc/version)
        echo -e "${BLUE}WSL版本: $WSL_VERSION${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  未检测到WSL环境，但可以继续编译${NC}"
        return 0
    fi
}

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"
    
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装${NC}"
        return 1
    fi
    
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境: $GO_VERSION${NC}"
    
    # 显示Go环境信息
    echo -e "${BLUE}GOOS: $(go env GOOS)${NC}"
    echo -e "${BLUE}GOARCH: $(go env GOARCH)${NC}"
    echo -e "${BLUE}GOROOT: $(go env GOROOT)${NC}"
    echo -e "${BLUE}GOPATH: $(go env GOPATH)${NC}"
    
    return 0
}

# 设置Go代理
setup_proxy() {
    echo -e "${YELLOW}🌐 设置Go代理...${NC}"
    
    # WSL环境推荐使用国内代理
    export GOPROXY=https://goproxy.cn,direct
    export GOSUMDB=sum.golang.google.cn
    export GO111MODULE=on
    
    echo -e "${BLUE}GOPROXY: $GOPROXY${NC}"
    echo -e "${BLUE}GOSUMDB: $GOSUMDB${NC}"
    
    # 测试代理连接
    echo -e "${YELLOW}测试代理连接...${NC}"
    if curl -s --connect-timeout 5 https://goproxy.cn > /dev/null; then
        echo -e "${GREEN}✅ 代理连接正常${NC}"
    else
        echo -e "${YELLOW}⚠️  代理连接可能有问题，尝试直连${NC}"
        export GOPROXY=direct
        export GOSUMDB=off
    fi
}

# 清理并重新下载依赖
fix_dependencies() {
    echo -e "${YELLOW}🧹 清理并重新下载依赖...${NC}"
    
    # 清理现有文件
    rm -f go.sum
    echo -e "${BLUE}已删除 go.sum${NC}"
    
    # 清理模块缓存
    go clean -modcache
    echo -e "${BLUE}已清理模块缓存${NC}"
    
    # 重新整理依赖
    echo -e "${BLUE}重新整理依赖...${NC}"
    go mod tidy
    
    # 逐个下载依赖
    echo -e "${BLUE}下载 github.com/google/uuid...${NC}"
    go get github.com/google/uuid@v1.3.0
    
    echo -e "${BLUE}下载 github.com/gorilla/mux...${NC}"
    go get github.com/gorilla/mux@v1.8.0
    
    echo -e "${BLUE}下载 github.com/mattn/go-sqlite3...${NC}"
    go get github.com/mattn/go-sqlite3@v1.14.17
    
    # 再次整理依赖
    go mod tidy
    
    # 下载所有依赖
    echo -e "${BLUE}下载所有依赖...${NC}"
    go mod download
    
    # 验证依赖
    echo -e "${BLUE}验证依赖...${NC}"
    if go mod verify; then
        echo -e "${GREEN}✅ 依赖验证成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 依赖验证失败${NC}"
        return 1
    fi
}

# 测试编译
test_compile() {
    echo -e "${YELLOW}🧪 测试编译...${NC}"
    
    # 测试本地编译
    go build -o test-binary
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 本地编译测试成功${NC}"
        rm -f test-binary
        return 0
    else
        echo -e "${RED}❌ 本地编译测试失败${NC}"
        return 1
    fi
}

# 编译AMD64版本
build_amd64() {
    echo -e "${YELLOW}🔨 编译AMD64版本...${NC}"

    # 设置编译环境变量
    export GOOS=linux
    export GOARCH=amd64
    export CGO_ENABLED=1

    echo -e "${BLUE}编译目标: $GOOS/$GOARCH (CGO启用)${NC}"

    # 编译
    go build -ldflags="-s -w" -o cns-amd64-linux

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ AMD64编译成功: cns-amd64-linux${NC}"

        # 显示文件信息
        if [ -f "cns-amd64-linux" ]; then
            file_size=$(du -h cns-amd64-linux | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"

            # 检查文件类型
            if command -v file &> /dev/null; then
                file_info=$(file cns-amd64-linux)
                echo -e "${BLUE}📋 文件信息: $file_info${NC}"
            fi
        fi
        return 0
    else
        echo -e "${RED}❌ AMD64编译失败${NC}"
        return 1
    fi
}

# 编译ARM64版本（CGO启用）
build_arm64_cgo() {
    echo -e "${YELLOW}🔨 编译ARM64版本 (CGO启用)...${NC}"

    # 检查交叉编译工具
    if command -v aarch64-linux-gnu-gcc &> /dev/null; then
        export CC=aarch64-linux-gnu-gcc
        export CGO_ENABLED=1
        echo -e "${BLUE}使用交叉编译器: $CC${NC}"
    else
        echo -e "${YELLOW}⚠️  未找到ARM64交叉编译器，使用CGO_ENABLED=0${NC}"
        export CGO_ENABLED=0
    fi

    # 设置交叉编译环境变量
    export GOOS=linux
    export GOARCH=arm64

    echo -e "${BLUE}编译目标: $GOOS/$GOARCH (CGO=$CGO_ENABLED)${NC}"

    # 编译
    go build -ldflags="-s -w" -o cns-arm64-linux

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM64编译成功: cns-arm64-linux${NC}"

        # 显示文件信息
        if [ -f "cns-arm64-linux" ]; then
            file_size=$(du -h cns-arm64-linux | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"

            # 检查文件类型
            if command -v file &> /dev/null; then
                file_info=$(file cns-arm64-linux)
                echo -e "${BLUE}📋 文件信息: $file_info${NC}"
            fi
        fi
        return 0
    else
        echo -e "${RED}❌ ARM64编译失败${NC}"
        return 1
    fi
}

# 编译ARM64版本（无CGO，纯Go数据库）
build_arm64_nocgo() {
    echo -e "${YELLOW}🔨 编译ARM64版本 (纯Go数据库)...${NC}"

    # 备份原文件
    cp db.go db_sqlite.go

    # 创建纯Go数据库版本
    create_pure_go_db

    # 设置交叉编译环境变量
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0

    echo -e "${BLUE}编译目标: $GOOS/$GOARCH (纯Go)${NC}"

    # 编译
    go build -ldflags="-s -w" -o cns-arm64-linux-pure

    # 恢复原文件
    mv db_sqlite.go db.go

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM64纯Go版本编译成功: cns-arm64-linux-pure${NC}"

        # 显示文件信息
        if [ -f "cns-arm64-linux-pure" ]; then
            file_size=$(du -h cns-arm64-linux-pure | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"
        fi
        return 0
    else
        echo -e "${RED}❌ ARM64纯Go版本编译失败${NC}"
        return 1
    fi
}

# 编译ARM32版本
build_arm32() {
    echo -e "${YELLOW}🔨 编译ARM32版本...${NC}"
    
    # 设置交叉编译环境变量
    export GOOS=linux
    export GOARCH=arm
    export CGO_ENABLED=0
    
    echo -e "${BLUE}编译目标: $GOOS/$GOARCH${NC}"
    
    # 编译
    go build -ldflags="-s -w" -o cns-arm32-linux
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM32编译成功: cns-arm32-linux${NC}"
        
        # 显示文件信息
        if [ -f "cns-arm32-linux" ]; then
            file_size=$(du -h cns-arm32-linux | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"
        fi
        return 0
    else
        echo -e "${RED}❌ ARM32编译失败${NC}"
        return 1
    fi
}

# 编译Windows ARM版本
build_windows_arm() {
    echo -e "${YELLOW}🔨 编译Windows ARM版本...${NC}"
    
    # ARM64 Windows
    export GOOS=windows
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    go build -ldflags="-s -w" -o cns-arm64-windows.exe
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Windows ARM64编译成功: cns-arm64-windows.exe${NC}"
    else
        echo -e "${RED}❌ Windows ARM64编译失败${NC}"
    fi
}

# 创建部署包
create_deployment_package() {
    echo -e "${YELLOW}📦 创建部署包...${NC}"
    
    # 创建部署目录
    mkdir -p deployment
    
    # 复制可执行文件
    cp cns-arm*-linux deployment/ 2>/dev/null || true
    cp cns-arm*-windows.exe deployment/ 2>/dev/null || true
    
    # 复制配置文件
    cp config_arm.json deployment/ 2>/dev/null || cp config.json deployment/config_arm.json
    
    # 创建启动脚本
    cat > deployment/start.sh << 'EOF'
#!/bin/bash
echo "🚀 启动 CNS 流量控制系统"
echo "========================"

# 检测架构并选择对应的可执行文件
ARCH=$(uname -m)
case $ARCH in
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux" ]; then
            CNS_BINARY="./cns-arm64-linux"
        else
            echo "❌ 未找到ARM64可执行文件"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux" ]; then
            CNS_BINARY="./cns-arm32-linux"
        else
            echo "❌ 未找到ARM32可执行文件"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        exit 1
        ;;
esac

chmod +x $CNS_BINARY
echo "使用可执行文件: $CNS_BINARY"
echo "配置文件: config_arm.json"
echo ""
echo "服务地址:"
echo "  代理服务: localhost:1254"
echo "  API服务: http://localhost:8080"
echo "  Web管理: http://localhost:8081"
echo ""

$CNS_BINARY -json=config_arm.json
EOF
    
    chmod +x deployment/start.sh
    
    # 创建README
    cat > deployment/README.md << 'EOF'
# CNS 流量控制系统 - ARM部署包

## 文件说明
- `cns-arm64-linux` - ARM64 Linux可执行文件
- `cns-arm32-linux` - ARM32 Linux可执行文件  
- `config_arm.json` - ARM优化配置文件
- `start.sh` - 自动启动脚本

## 使用方法
1. 上传整个目录到ARM设备
2. 运行: `./start.sh`

## 访问地址
- Web管理: http://设备IP:8081
- API服务: http://设备IP:8080
- 代理服务: 设备IP:1254

## 系统要求
- Linux ARM64/ARM32
- 最少512MB内存
- 网络连接
EOF
    
    echo -e "${GREEN}✅ 部署包已创建在 deployment/ 目录${NC}"
}

# 显示结果
show_results() {
    echo -e "${GREEN}📋 编译结果总结${NC}"
    echo "===================="
    
    success_count=0
    
    if [ -f "cns-arm64-linux" ]; then
        size=$(du -h cns-arm64-linux | cut -f1)
        echo -e "${GREEN}✅ ARM64 Linux: cns-arm64-linux ($size)${NC}"
        ((success_count++))
    fi
    
    if [ -f "cns-arm32-linux" ]; then
        size=$(du -h cns-arm32-linux | cut -f1)
        echo -e "${GREEN}✅ ARM32 Linux: cns-arm32-linux ($size)${NC}"
        ((success_count++))
    fi
    
    if [ -f "cns-arm64-windows.exe" ]; then
        size=$(du -h cns-arm64-windows.exe | cut -f1)
        echo -e "${GREEN}✅ Windows ARM64: cns-arm64-windows.exe ($size)${NC}"
        ((success_count++))
    fi
    
    echo ""
    echo -e "${BLUE}🎯 编译成功: $success_count 个版本${NC}"
    
    if [ $success_count -gt 0 ]; then
        echo ""
        echo -e "${YELLOW}🚀 快速测试:${NC}"
        echo "  ./cns-arm64-linux -json=config_arm.json"
        echo ""
        echo -e "${YELLOW}📦 部署包:${NC}"
        echo "  查看 deployment/ 目录"
        echo ""
        echo -e "${YELLOW}🌐 访问地址:${NC}"
        echo "  Web管理: http://localhost:8081"
        echo "  API服务: http://localhost:8080"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}在WSL环境中编译CNS ARM版本${NC}"
    echo ""
    
    # 检查环境
    check_wsl
    if ! check_go; then
        exit 1
    fi
    
    # 设置代理
    setup_proxy
    
    # 修复依赖
    if ! fix_dependencies; then
        echo -e "${RED}❌ 依赖修复失败${NC}"
        exit 1
    fi
    
    # 测试编译
    if ! test_compile; then
        echo -e "${RED}❌ 编译测试失败${NC}"
        exit 1
    fi
    
    # 编译ARM版本
    echo ""
    echo -e "${BLUE}开始编译ARM版本...${NC}"
    
    build_arm64
    build_arm32
    build_windows_arm
    
    # 创建部署包
    create_deployment_package
    
    # 显示结果
    show_results
    
    echo ""
    echo -e "${GREEN}🎉 WSL ARM编译完成！${NC}"
}

# 运行主函数
main "$@"
