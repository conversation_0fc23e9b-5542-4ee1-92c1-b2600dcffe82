# CNS 流量控制系统 Makefile

# 变量定义
BINARY_NAME=cns
VERSION=1.0.0
BUILD_TIME=$(shell date +%Y%m%d%H%M%S)
LDFLAGS=-ldflags="-s -w -X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME)"

# 默认目标
.PHONY: all
all: build

# 检查Go环境
.PHONY: check
check:
	@echo "📦 检查Go环境..."
	@go version || (echo "❌ Go未安装" && exit 1)
	@echo "✅ Go环境检查通过"

# 安装依赖
.PHONY: deps
deps:
	@echo "📥 安装依赖..."
	@go mod tidy
	@echo "✅ 依赖安装完成"

# 本地编译
.PHONY: build
build: check deps
	@echo "🔨 编译本地版本..."
	go build $(LDFLAGS) -o $(BINARY_NAME)
	@echo "✅ 编译完成: $(BINARY_NAME)"

# ARM32 Linux编译
.PHONY: arm32
arm32: check deps
	@echo "🔨 编译 ARM32 Linux..."
	GOOS=linux GOARCH=arm CGO_ENABLED=0 go build $(LDFLAGS) -o $(BINARY_NAME)-arm32-linux
	@echo "✅ ARM32 Linux编译完成"

# ARM64 Linux编译
.PHONY: arm64
arm64: check deps
	@echo "🔨 编译 ARM64 Linux..."
	GOOS=linux GOARCH=arm64 CGO_ENABLED=0 go build $(LDFLAGS) -o $(BINARY_NAME)-arm64-linux
	@echo "✅ ARM64 Linux编译完成"

# ARM32 Windows编译
.PHONY: arm32-win
arm32-win: check deps
	@echo "🔨 编译 ARM32 Windows..."
	GOOS=windows GOARCH=arm CGO_ENABLED=0 go build $(LDFLAGS) -o $(BINARY_NAME)-arm32-windows.exe
	@echo "✅ ARM32 Windows编译完成"

# ARM64 Windows编译
.PHONY: arm64-win
arm64-win: check deps
	@echo "🔨 编译 ARM64 Windows..."
	GOOS=windows GOARCH=arm64 CGO_ENABLED=0 go build $(LDFLAGS) -o $(BINARY_NAME)-arm64-windows.exe
	@echo "✅ ARM64 Windows编译完成"

# 编译所有ARM版本
.PHONY: arm-all
arm-all: arm32 arm64 arm32-win arm64-win
	@echo "✅ 所有ARM版本编译完成"

# 树莓派专用 (ARM64)
.PHONY: raspberry
raspberry: arm64
	@echo "🍓 树莓派版本编译完成"
	@cp $(BINARY_NAME)-arm64-linux $(BINARY_NAME)-raspberry

# 创建发布包
.PHONY: release
release: arm-all
	@echo "📦 创建发布包..."
	@mkdir -p release
	@cp $(BINARY_NAME)-* release/ 2>/dev/null || true
	@cp config_arm.json release/ 2>/dev/null || cp config.json release/config_arm.json
	@cp TRAFFIC_CONTROL_README.md release/ 2>/dev/null || true
	@cp start_arm_linux.sh release/ 2>/dev/null || true
	@cp start_arm_windows.bat release/ 2>/dev/null || true
	@echo "✅ 发布包已创建在 release/ 目录"

# 清理编译文件
.PHONY: clean
clean:
	@echo "🧹 清理编译文件..."
	@rm -f $(BINARY_NAME) $(BINARY_NAME)-* *.exe
	@rm -rf release/
	@echo "✅ 清理完成"

# 测试编译
.PHONY: test-build
test-build:
	@echo "🧪 测试编译..."
	@go build -o /tmp/$(BINARY_NAME)-test
	@echo "✅ 测试编译成功"
	@rm -f /tmp/$(BINARY_NAME)-test

# 显示帮助
.PHONY: help
help:
	@echo "CNS 流量控制系统编译帮助"
	@echo "========================"
	@echo ""
	@echo "可用目标:"
	@echo "  build      - 编译本地版本"
	@echo "  arm32      - 编译 ARM32 Linux"
	@echo "  arm64      - 编译 ARM64 Linux"
	@echo "  arm32-win  - 编译 ARM32 Windows"
	@echo "  arm64-win  - 编译 ARM64 Windows"
	@echo "  arm-all    - 编译所有ARM版本"
	@echo "  raspberry  - 编译树莓派版本"
	@echo "  release    - 创建发布包"
	@echo "  clean      - 清理编译文件"
	@echo "  test-build - 测试编译"
	@echo "  help       - 显示此帮助"
	@echo ""
	@echo "示例:"
	@echo "  make arm64     # 编译ARM64 Linux版本"
	@echo "  make raspberry # 编译树莓派版本"
	@echo "  make release   # 创建完整发布包"

# 快速编译常用ARM设备
.PHONY: quick-arm
quick-arm:
	@echo "⚡ 快速编译常用ARM设备版本..."
	@echo "1. ARM64 Linux (树莓派4/5, 大多数ARM服务器)"
	@$(MAKE) arm64
	@echo "2. ARM32 Linux (树莓派2/3, 旧ARM设备)"
	@$(MAKE) arm32
	@echo "✅ 常用ARM版本编译完成"

# 验证编译结果
.PHONY: verify
verify:
	@echo "🔍 验证编译结果..."
	@for file in $(BINARY_NAME)-*; do \
		if [ -f "$$file" ]; then \
			echo "✅ $$file ($(shell du -h $$file | cut -f1))"; \
			file $$file 2>/dev/null || echo "   文件类型检查需要file命令"; \
		fi; \
	done
	@echo "验证完成"
