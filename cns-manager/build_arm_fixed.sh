#!/bin/bash

# CNS ARM 修复编译脚本

echo "🔧 CNS ARM 修复编译脚本"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 清理环境
cleanup_environment() {
    echo -e "${YELLOW}🧹 清理编译环境...${NC}"
    
    # 删除所有备份和临时文件
    rm -f db_sqlite.go
    rm -f db_sqlite.go.bak
    rm -f go.mod.bak
    rm -f api_full.go
    rm -f db_full.go
    rm -f web_admin_full.go
    rm -f *_simple.go
    rm -f *_nodeps.go
    
    echo -e "${GREEN}✅ 环境清理完成${NC}"
}

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"
    
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装${NC}"
        return 1
    fi
    
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境: $GO_VERSION${NC}"
    return 0
}

# 设置Go代理
setup_proxy() {
    echo -e "${YELLOW}🌐 设置Go代理...${NC}"
    
    export GOPROXY=https://goproxy.cn,direct
    export GOSUMDB=sum.golang.google.cn
    export GO111MODULE=on
    
    echo -e "${BLUE}GOPROXY: $GOPROXY${NC}"
}

# 创建纯Go数据库版本
create_pure_go_database() {
    echo -e "${YELLOW}📝 创建纯Go数据库版本...${NC}"
    
    # 备份原始的db.go
    cp db.go db_original.go
    
    # 创建纯Go版本的db.go
    cat > db.go << 'EOF'
package main

import (
    "encoding/json"
    "log"
    "os"
    "sync"
    "time"
)

// 纯Go内存数据库
type MemoryDB struct {
    Users           map[string]*User           `json:"users"`
    ConnectionLogs  []ConnectionLog            `json:"connection_logs"`
    TrafficStats    []TrafficStat              `json:"traffic_stats"`
    mutex           sync.RWMutex
}

type User struct {
    UserID    string     `json:"user_id"`
    Username  string     `json:"username"`
    Password  string     `json:"password"`
    Quota     int64      `json:"quota"`
    Upload    int64      `json:"upload"`
    Download  int64      `json:"download"`
    CreatedAt time.Time  `json:"created_at"`
    ExpireAt  *time.Time `json:"expire_at,omitempty"`
    Status    int        `json:"status"`
    WechatID  string     `json:"wechat_id"`
}

type ConnectionLog struct {
    ID             int64     `json:"id"`
    UserID         string    `json:"user_id"`
    ClientIP       string    `json:"client_ip"`
    TargetHost     string    `json:"target_host"`
    ConnectTime    time.Time `json:"connect_time"`
    DisconnectTime *time.Time `json:"disconnect_time,omitempty"`
    Upload         int64     `json:"upload"`
    Download       int64     `json:"download"`
}

type TrafficStat struct {
    ID          int64  `json:"id"`
    UserID      string `json:"user_id"`
    Date        string `json:"date"`
    Upload      int64  `json:"upload"`
    Download    int64  `json:"download"`
    Connections int    `json:"connections"`
}

var (
    memDB      *MemoryDB
    dbFile     = "cns_users.json"
    nextLogID  = int64(1)
    nextStatID = int64(1)
)

// 初始化数据库
func initDatabase() {
    memDB = &MemoryDB{
        Users:          make(map[string]*User),
        ConnectionLogs: make([]ConnectionLog, 0),
        TrafficStats:   make([]TrafficStat, 0),
    }
    
    // 尝试从文件加载数据
    if data, err := os.ReadFile(dbFile); err == nil {
        if err := json.Unmarshal(data, memDB); err != nil {
            log.Printf("加载数据库文件失败: %v", err)
        } else {
            log.Println("数据库文件加载成功")
        }
    }
    
    // 设置下一个ID
    for _, log := range memDB.ConnectionLogs {
        if log.ID >= nextLogID {
            nextLogID = log.ID + 1
        }
    }
    for _, stat := range memDB.TrafficStats {
        if stat.ID >= nextStatID {
            nextStatID = stat.ID + 1
        }
    }
    
    log.Println("数据库初始化完成 (纯Go版本)")
    
    // 定期保存数据
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        for range ticker.C {
            saveDatabase()
        }
    }()
}

// 保存数据库到文件
func saveDatabase() {
    memDB.mutex.RLock()
    data, err := json.MarshalIndent(memDB, "", "  ")
    memDB.mutex.RUnlock()
    
    if err != nil {
        log.Printf("序列化数据库失败: %v", err)
        return
    }
    
    if err := os.WriteFile(dbFile, data, 0644); err != nil {
        log.Printf("保存数据库文件失败: %v", err)
    }
}

// 添加用户
func addUser(userID, username, password string, quota int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user := &User{
        UserID:    userID,
        Username:  username,
        Password:  password,
        Quota:     quota,
        Upload:    0,
        Download:  0,
        CreatedAt: time.Now(),
        Status:    1,
    }
    
    memDB.Users[userID] = user
    log.Printf("添加用户: %s", username)
    return nil
}

// 获取用户流量
func getUserTraffic(userID string) (upload, download, quota int64, err error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return 0, 0, 1073741824, nil
    }
    
    return user.Upload, user.Download, user.Quota, nil
}

// 重置用户流量
func resetUserTraffic(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.Upload = 0
    user.Download = 0
    log.Printf("重置用户流量: %s", userID)
    return nil
}

// 删除用户
func deleteUser(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    delete(memDB.Users, userID)
    log.Printf("删除用户: %s", userID)
    return nil
}

// 绑定微信ID
func bindWechatID(userID, wechatID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.WechatID = wechatID
    log.Printf("绑定微信ID: %s -> %s", userID, wechatID)
    return nil
}

// 根据微信ID获取用户ID
func getUserIDByWechatID(wechatID string) (string, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    for _, user := range memDB.Users {
        if user.WechatID == wechatID {
            return user.UserID, nil
        }
    }
    
    return "", nil
}

// 记录连接日志
func logConnection(userID, clientIP, targetHost string) (int64, error) {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    logID := nextLogID
    nextLogID++
    
    log := ConnectionLog{
        ID:          logID,
        UserID:      userID,
        ClientIP:    clientIP,
        TargetHost:  targetHost,
        ConnectTime: time.Now(),
        Upload:      0,
        Download:    0,
    }
    
    memDB.ConnectionLogs = append(memDB.ConnectionLogs, log)
    return logID, nil
}

// 更新连接日志
func updateConnectionLog(logID int64, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    for i := range memDB.ConnectionLogs {
        if memDB.ConnectionLogs[i].ID == logID {
            now := time.Now()
            memDB.ConnectionLogs[i].DisconnectTime = &now
            memDB.ConnectionLogs[i].Upload = upload
            memDB.ConnectionLogs[i].Download = download
            break
        }
    }
    
    return nil
}

// 获取用户连接历史
func getUserConnectionHistory(userID string, limit int) ([]map[string]interface{}, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    var history []map[string]interface{}
    count := 0
    
    for i := len(memDB.ConnectionLogs) - 1; i >= 0 && count < limit; i-- {
        log := memDB.ConnectionLogs[i]
        if log.UserID == userID {
            record := map[string]interface{}{
                "client_ip":       log.ClientIP,
                "target_host":     log.TargetHost,
                "connect_time":    log.ConnectTime.Format("2006-01-02 15:04:05"),
                "disconnect_time": "",
                "upload":          log.Upload,
                "download":        log.Download,
            }
            
            if log.DisconnectTime != nil {
                record["disconnect_time"] = log.DisconnectTime.Format("2006-01-02 15:04:05")
            }
            
            history = append(history, record)
            count++
        }
    }
    
    return history, nil
}

// 更新每日流量统计
func updateDailyTrafficStats(userID string, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    today := time.Now().Format("2006-01-02")
    
    // 查找今天的统计记录
    for i := range memDB.TrafficStats {
        if memDB.TrafficStats[i].UserID == userID && memDB.TrafficStats[i].Date == today {
            memDB.TrafficStats[i].Upload += upload
            memDB.TrafficStats[i].Download += download
            memDB.TrafficStats[i].Connections++
            return nil
        }
    }
    
    // 创建新的统计记录
    stat := TrafficStat{
        ID:          nextStatID,
        UserID:      userID,
        Date:        today,
        Upload:      upload,
        Download:    download,
        Connections: 1,
    }
    nextStatID++
    
    memDB.TrafficStats = append(memDB.TrafficStats, stat)
    return nil
}

// 获取用户每日统计
func getUserDailyStats(userID string, days int) ([]map[string]interface{}, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    var stats []map[string]interface{}
    count := 0
    
    for i := len(memDB.TrafficStats) - 1; i >= 0 && count < days; i-- {
        stat := memDB.TrafficStats[i]
        if stat.UserID == userID {
            record := map[string]interface{}{
                "date":        stat.Date,
                "upload":      stat.Upload,
                "download":    stat.Download,
                "connections": stat.Connections,
                "total":       stat.Upload + stat.Download,
            }
            
            stats = append(stats, record)
            count++
        }
    }
    
    return stats, nil
}

// 更新用户信息
func updateUserInfo(userID string, quota *int64, status *int) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    if quota != nil {
        user.Quota = *quota
    }
    
    if status != nil {
        user.Status = *status
    }
    
    return nil
}

// 设置用户过期时间
func setUserExpire(userID string, expireTime time.Time) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.ExpireAt = &expireTime
    return nil
}

// 获取总用户数
func getTotalUserCount() (int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    return len(memDB.Users), nil
}

// 获取用户列表
func getUserList(page, limit int) ([]map[string]interface{}, int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    total := len(memDB.Users)
    var users []map[string]interface{}
    
    count := 0
    skip := (page - 1) * limit
    
    for _, user := range memDB.Users {
        if count < skip {
            count++
            continue
        }
        
        if len(users) >= limit {
            break
        }
        
        userMap := map[string]interface{}{
            "user_id":      user.UserID,
            "username":     user.Username,
            "quota":        user.Quota,
            "upload":       user.Upload,
            "download":     user.Download,
            "created_at":   user.CreatedAt.Format("2006-01-02 15:04:05"),
            "expire_at":    "",
            "status":       user.Status,
            "wechat_id":    user.WechatID,
            "used_percent": float64(user.Upload+user.Download) / float64(user.Quota) * 100,
        }
        
        if user.ExpireAt != nil {
            userMap["expire_at"] = user.ExpireAt.Format("2006-01-02 15:04:05")
        }
        
        users = append(users, userMap)
        count++
    }
    
    return users, total, nil
}

// 更新用户流量到数据库
func updateUserTrafficInDB(userID string, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.Upload += upload
    user.Download += download
    return nil
}
EOF
    
    echo -e "${GREEN}✅ 纯Go数据库版本创建完成${NC}"
}

# 创建纯Go版本的go.mod
create_pure_go_mod() {
    echo -e "${YELLOW}📝 创建纯Go版本的go.mod...${NC}"
    
    # 备份原始go.mod
    cp go.mod go.mod.original
    
    cat > go.mod << 'EOF'
module cns

go 1.18

require (
    github.com/google/uuid v1.3.0
    github.com/gorilla/mux v1.8.0
)
EOF

    cat > go.sum << 'EOF'
github.com/google/uuid v1.3.0 h1:t6JiXgmwXMjEs8VusXIJk2BXHsn+wx8BZdTaoZ5fu7I=
github.com/google/uuid v1.3.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=
EOF
    
    echo -e "${GREEN}✅ 纯Go版本的go.mod创建完成${NC}"
}

# 恢复原始文件
restore_original_files() {
    echo -e "${YELLOW}🔄 恢复原始文件...${NC}"
    
    if [ -f "db_original.go" ]; then
        mv db_original.go db.go
    fi
    
    if [ -f "go.mod.original" ]; then
        mv go.mod.original go.mod
    fi
    
    echo -e "${GREEN}✅ 原始文件恢复完成${NC}"
}

# 编译ARM64版本
build_arm64_pure() {
    echo -e "${YELLOW}🔨 编译ARM64纯Go版本...${NC}"
    
    # 设置编译环境
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    echo -e "${BLUE}编译目标: $GOOS/$GOARCH (CGO_ENABLED=$CGO_ENABLED)${NC}"
    
    # 下载依赖
    go mod tidy
    go mod download
    
    # 编译
    go build -ldflags="-s -w" -o cns-arm64-linux-pure
    
    # 检查编译结果
    if [ -f "cns-arm64-linux-pure" ] && [ -s "cns-arm64-linux-pure" ]; then
        echo -e "${GREEN}✅ ARM64纯Go版本编译成功: cns-arm64-linux-pure${NC}"
        
        # 显示文件信息
        file_size=$(du -h cns-arm64-linux-pure | cut -f1)
        echo -e "${BLUE}📊 文件大小: $file_size${NC}"
        
        if command -v file &> /dev/null; then
            file_info=$(file cns-arm64-linux-pure)
            echo -e "${BLUE}📋 文件信息: $file_info${NC}"
        fi
        
        return 0
    else
        echo -e "${RED}❌ ARM64纯Go版本编译失败${NC}"
        return 1
    fi
}

# 编译ARM32版本
build_arm32_pure() {
    echo -e "${YELLOW}🔨 编译ARM32纯Go版本...${NC}"
    
    # 设置编译环境
    export GOOS=linux
    export GOARCH=arm
    export CGO_ENABLED=0
    
    echo -e "${BLUE}编译目标: $GOOS/$GOARCH (CGO_ENABLED=$CGO_ENABLED)${NC}"
    
    # 编译
    go build -ldflags="-s -w" -o cns-arm32-linux-pure
    
    # 检查编译结果
    if [ -f "cns-arm32-linux-pure" ] && [ -s "cns-arm32-linux-pure" ]; then
        echo -e "${GREEN}✅ ARM32纯Go版本编译成功: cns-arm32-linux-pure${NC}"
        
        # 显示文件信息
        file_size=$(du -h cns-arm32-linux-pure | cut -f1)
        echo -e "${BLUE}📊 文件大小: $file_size${NC}"
        
        return 0
    else
        echo -e "${RED}❌ ARM32纯Go版本编译失败${NC}"
        return 1
    fi
}

# 创建启动脚本
create_startup_scripts() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    cat > start_arm_pure.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (纯Go版本)"
echo "=================================="

# 自动检测架构
ARCH=$(uname -m)
case $ARCH in
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux-pure" ]; then
            CNS_BINARY="./cns-arm64-linux-pure"
            echo "✅ 使用 ARM64 纯Go版本"
        else
            echo "❌ 未找到ARM64纯Go版本: cns-arm64-linux-pure"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux-pure" ]; then
            CNS_BINARY="./cns-arm32-linux-pure"
            echo "✅ 使用 ARM32 纯Go版本"
        else
            echo "❌ 未找到ARM32纯Go版本: cns-arm32-linux-pure"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        echo "支持的架构: aarch64, arm64, armv7l, arm"
        exit 1
        ;;
esac

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件 (config_arm.json 或 config.json)"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   数据存储: cns_users.json (纯Go版本)"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 特点:"
echo "   - 无CGO依赖，适合ARM设备"
echo "   - 数据存储在JSON文件中"
echo "   - 自动定期保存数据"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
EOF
    
    chmod +x start_arm_pure.sh
    echo -e "${GREEN}✅ 启动脚本创建完成: start_arm_pure.sh${NC}"
}

# 显示结果
show_results() {
    echo -e "${GREEN}📋 编译结果${NC}"
    echo "=============="
    
    success_count=0
    
    if [ -f "cns-arm64-linux-pure" ] && [ -s "cns-arm64-linux-pure" ]; then
        size=$(du -h cns-arm64-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM64 纯Go版本: cns-arm64-linux-pure ($size)${NC}"
        ((success_count++))
    fi
    
    if [ -f "cns-arm32-linux-pure" ] && [ -s "cns-arm32-linux-pure" ]; then
        size=$(du -h cns-arm32-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM32 纯Go版本: cns-arm32-linux-pure ($size)${NC}"
        ((success_count++))
    fi
    
    echo ""
    echo -e "${BLUE}🎯 成功编译: $success_count 个ARM版本${NC}"
    
    if [ $success_count -gt 0 ]; then
        echo ""
        echo -e "${BLUE}🚀 使用方法:${NC}"
        echo "  ./start_arm_pure.sh    # 自动选择架构启动"
        echo ""
        echo -e "${BLUE}或手动启动:${NC}"
        echo "  ./cns-arm64-linux-pure -json config_arm.json"
        echo "  ./cns-arm32-linux-pure -json config_arm.json"
        echo ""
        echo -e "${YELLOW}💡 特点:${NC}"
        echo "  - 无CGO依赖，解决ARM设备SQLite问题"
        echo "  - 数据存储在 cns_users.json 文件中"
        echo "  - 支持所有原有功能"
        echo "  - 自动定期保存数据"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}开始修复ARM编译问题...${NC}"
    echo ""
    
    # 检查Go环境
    if ! check_go; then
        exit 1
    fi
    
    # 设置代理
    setup_proxy
    
    # 清理环境
    cleanup_environment
    
    # 创建纯Go版本
    create_pure_go_database
    create_pure_go_mod
    
    # 编译ARM版本
    echo ""
    echo -e "${BLUE}开始编译ARM版本...${NC}"
    
    build_arm64_pure
    build_arm32_pure
    
    # 恢复原始文件
    restore_original_files
    
    # 创建启动脚本
    create_startup_scripts
    
    # 显示结果
    show_results
    
    echo ""
    echo -e "${GREEN}🎉 ARM编译修复完成！${NC}"
}

# 运行主函数
main "$@"
