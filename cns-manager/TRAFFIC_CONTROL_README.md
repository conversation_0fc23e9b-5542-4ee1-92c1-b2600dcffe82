# CNS 流量控制系统

## 概述

CNS 流量控制系统是在原有 CuteBi Network Server 基础上构建的用户管理和流量控制系统。它提供了完整的用户认证、流量统计、配额管理和监控功能。

## 主要功能

### 1. 用户管理
- 用户注册和认证
- 流量配额设置
- 用户状态管理（启用/禁用）
- 用户过期时间设置
- 微信ID绑定

### 2. 流量控制
- 实时流量监控
- 流量配额检查
- 自动断开超额连接
- 流量统计记录

### 3. 数据统计
- 实时流量统计
- 每日流量统计
- 连接历史记录
- 系统状态监控

### 4. API接口
- RESTful API
- 用户管理接口
- 流量查询接口
- 系统监控接口

### 5. Web管理界面
- 用户友好的管理界面
- 实时数据展示
- 用户管理操作
- 流量统计图表

## 安装和配置

### 1. 依赖安装

```bash
cd cns-manager
go mod tidy
```

### 2. 配置文件

编辑 `config.json` 文件：

```json
{
    "Tcp_timeout": 300,
    "Udp_timeout": 30,
    "Listen_addr": [":1254"],
    "proxy_key": "Host",
    "encrypt_password": "your_password",
    "Enable_dns_tcpOverUdp": true,
    "Enable_httpDNS": true,
    "Enable_TFO": false,
    "Enable_traffic_control": true,
    "API_port": ":8080",
    "Tls": {
        "listen_addr": [":8978"],
        "AutoCertHosts": ["example.com"]
    }
}
```

### 3. 编译和运行

```bash
go build -o cns
./cns -json=config.json
```

## API 使用说明

### 用户管理 API

#### 1. 添加用户
```bash
curl -X POST http://localhost:8080/api/user/add \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass",
    "quota": 1073741824
  }'
```

#### 2. 用户登录验证
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass"
  }'
```

#### 3. 获取用户信息
```bash
curl http://localhost:8080/api/user/info/{userID}
```

#### 4. 获取用户流量
```bash
curl http://localhost:8080/api/user/traffic?user_id={userID}
```

#### 5. 重置用户流量
```bash
curl -X POST http://localhost:8080/api/user/reset?user_id={userID}
```

#### 6. 设置用户过期时间
```bash
curl -X POST http://localhost:8080/api/user/expire/{userID} \
  -H "Content-Type: application/json" \
  -d '{
    "expire_at": "2024-12-31 23:59:59"
  }'
```

### 流量统计 API

#### 1. 实时流量统计
```bash
curl http://localhost:8080/api/stats/realtime/{userID}
```

#### 2. 每日流量统计
```bash
curl http://localhost:8080/api/stats/daily/{userID}?days=7
```

#### 3. 连接历史
```bash
curl http://localhost:8080/api/stats/history/{userID}?limit=50
```

### 系统监控 API

#### 1. 系统状态
```bash
curl http://localhost:8080/api/system/status
```

#### 2. 用户列表
```bash
curl http://localhost:8080/api/system/users?page=1&limit=20
```

## Web 管理界面

访问 `http://localhost:8081` 打开Web管理界面。

### 功能页面：
- **仪表板**: 系统概览和实时状态
- **用户管理**: 用户列表和操作
- **流量统计**: 详细的流量分析

## 客户端配置

### 1. 基本代理配置
客户端需要配置代理服务器地址和端口，例如：
- HTTP代理: `localhost:1254`
- HTTPS代理: `localhost:8978`

### 2. 用户认证
如果启用了流量控制，客户端需要在HTTP请求头中包含用户ID：
```
User-ID: {your_user_id}
```

或者使用Basic认证：
```
Authorization: Basic {base64_encoded_username:password}
```

## 数据库结构

系统使用SQLite数据库，包含以下表：

### users 表
- user_id: 用户唯一标识
- username: 用户名
- password: 密码
- quota: 流量配额（字节）
- upload: 已上传流量
- download: 已下载流量
- created_at: 创建时间
- expire_at: 过期时间
- status: 用户状态
- wechat_id: 微信ID

### connection_logs 表
- id: 连接ID
- user_id: 用户ID
- client_ip: 客户端IP
- target_host: 目标主机
- connect_time: 连接时间
- disconnect_time: 断开时间
- upload: 上传流量
- download: 下载流量

### traffic_stats 表
- id: 统计ID
- user_id: 用户ID
- date: 日期
- upload: 上传流量
- download: 下载流量
- connections: 连接数

## 监控和日志

### 1. 日志文件
系统日志包含：
- 用户认证记录
- 流量超额警告
- 连接建立和断开
- 系统错误信息

### 2. 实时监控
- 在线用户数量
- 实时流量统计
- 系统资源使用

## 安全建议

1. **密码安全**: 使用强密码并定期更换
2. **网络安全**: 在生产环境中使用HTTPS
3. **访问控制**: 限制API和Web界面的访问
4. **数据备份**: 定期备份用户数据和配置
5. **监控告警**: 设置流量异常告警

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件权限
   - 确认SQLite驱动正确安装

2. **API服务无法访问**
   - 检查端口是否被占用
   - 确认防火墙设置

3. **流量统计不准确**
   - 检查系统时间同步
   - 确认流量记录协程正常运行

4. **用户认证失败**
   - 验证用户名密码正确性
   - 检查用户状态和过期时间

## 性能优化

1. **数据库优化**
   - 定期清理历史数据
   - 添加适当的索引

2. **内存管理**
   - 监控内存使用情况
   - 调整缓冲池大小

3. **并发处理**
   - 根据服务器性能调整连接数限制
   - 优化协程数量

## 更新和维护

1. **版本更新**: 定期检查更新
2. **数据备份**: 制定备份策略
3. **性能监控**: 持续监控系统性能
4. **安全更新**: 及时应用安全补丁

## 技术支持

如有问题，请查看：
1. 系统日志文件
2. API响应错误信息
3. Web界面错误提示
4. 数据库连接状态
