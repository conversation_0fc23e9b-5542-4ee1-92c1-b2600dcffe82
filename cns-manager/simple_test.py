#!/usr/bin/env python3
"""
CNS 简单连接测试脚本
测试本地代理连接和用户认证
"""

import requests
import json
import time
import base64
import socket
import sys

def test_api_connection():
    """测试API连接"""
    print("🔍 测试API连接...")
    try:
        response = requests.get("http://localhost:8080/api/system/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ API服务正常")
            print(f"   在线用户: {status['online_users']}")
            print(f"   总用户数: {status['total_users']}")
            return True
        else:
            print(f"❌ API服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_proxy_port():
    """测试代理端口"""
    print("\n🔍 测试代理端口...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 1254))
        sock.close()
        
        if result == 0:
            print("✅ 代理端口 1254 可访问")
            return True
        else:
            print("❌ 代理端口 1254 无法访问")
            return False
    except Exception as e:
        print(f"❌ 测试代理端口失败: {e}")
        return False

def create_test_user():
    """创建测试用户"""
    print("\n👤 创建测试用户...")
    
    username = f"test_{int(time.time())}"
    password = "test123"
    quota = 100 * 1024 * 1024  # 100MB
    
    try:
        data = {
            "username": username,
            "password": password,
            "quota": quota
        }
        
        response = requests.post("http://localhost:8080/api/user/add", json=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        
        user_id = result['user_id']
        print(f"✅ 测试用户创建成功")
        print(f"   用户名: {username}")
        print(f"   密码: {password}")
        print(f"   用户ID: {user_id}")
        
        return {
            'user_id': user_id,
            'username': username,
            'password': password,
            'quota': quota
        }
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        return None

def verify_user_in_db(user_id):
    """验证用户是否在数据库中"""
    print(f"\n🔍 验证用户是否在数据库中...")
    
    try:
        response = requests.get(f"http://localhost:8080/api/user/info/{user_id}", timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ 用户在数据库中找到")
            print(f"   用户ID: {user_info['user_id']}")
            print(f"   用户名: {user_info['username']}")
            print(f"   配额: {user_info['quota']} bytes")
            print(f"   上传: {user_info['upload']} bytes")
            print(f"   下载: {user_info['download']} bytes")
            print(f"   状态: {user_info['status']}")
            return user_info
        else:
            print(f"❌ 用户不在数据库中: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 验证用户失败: {e}")
        return None

def test_simple_proxy_connection(username, password):
    """测试简单的代理连接"""
    print(f"\n🔗 测试代理连接...")
    
    try:
        # 创建Basic认证字符串
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        print(f"   认证字符串: {username}:{password} -> {auth_string}")
        
        # 连接代理
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('localhost', 1254))
        
        # 发送CONNECT请求
        connect_request = f"CONNECT httpbin.org:80 HTTP/1.1\r\n"
        connect_request += f"Host: httpbin.org:80\r\n"
        connect_request += f"Authorization: Basic {auth_string}\r\n"
        connect_request += f"Proxy-Connection: keep-alive\r\n"
        connect_request += f"\r\n"
        
        print(f"📤 发送CONNECT请求:")
        print(f"   目标: httpbin.org:80")
        print(f"   认证: Basic {auth_string}")
        
        sock.send(connect_request.encode())
        
        # 接收响应
        response = sock.recv(1024).decode()
        print(f"📥 代理响应:")
        print(f"   {response.strip()}")
        
        if "200 Connection established" in response:
            print(f"✅ 代理连接建立成功")
            
            # 发送简单的HTTP请求
            http_request = "GET /get HTTP/1.1\r\n"
            http_request += "Host: httpbin.org\r\n"
            http_request += "User-Agent: CNS-Test-Client\r\n"
            http_request += "Connection: close\r\n"
            http_request += "\r\n"
            
            sock.send(http_request.encode())
            
            # 接收HTTP响应
            http_response = b""
            while True:
                try:
                    data = sock.recv(4096)
                    if not data:
                        break
                    http_response += data
                except socket.timeout:
                    break
            
            print(f"📊 HTTP响应大小: {len(http_response)} bytes")
            
            # 检查响应内容
            if b"httpbin.org" in http_response:
                print(f"✅ HTTP请求成功，收到正确响应")
                sock.close()
                return True
            else:
                print(f"⚠️ HTTP请求可能有问题")
                sock.close()
                return True  # 连接成功，即使HTTP响应有问题
        else:
            print(f"❌ 代理连接失败")
            sock.close()
            return False
            
    except Exception as e:
        print(f"❌ 代理连接异常: {e}")
        return False

def check_traffic_after_connection(user_id, before_upload=0, before_download=0):
    """检查连接后的流量变化"""
    print(f"\n📊 检查流量变化...")
    
    # 等待一下让流量统计更新
    time.sleep(3)
    
    try:
        response = requests.get(f"http://localhost:8080/api/user/info/{user_id}", timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            current_upload = user_info['upload']
            current_download = user_info['download']
            
            upload_change = current_upload - before_upload
            download_change = current_download - before_download
            
            print(f"📈 流量统计:")
            print(f"   连接前上传: {before_upload} bytes")
            print(f"   连接后上传: {current_upload} bytes")
            print(f"   上传变化: +{upload_change} bytes")
            print(f"   连接前下载: {before_download} bytes")
            print(f"   连接后下载: {current_download} bytes")
            print(f"   下载变化: +{download_change} bytes")
            
            if upload_change > 0 or download_change > 0:
                print(f"✅ 检测到流量更新")
                return True
            else:
                print(f"❌ 未检测到流量更新")
                return False
        else:
            print(f"❌ 获取用户信息失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 检查流量变化失败: {e}")
        return False

def cleanup_user(user_id):
    """清理测试用户"""
    print(f"\n🗑️ 清理测试用户...")
    
    try:
        response = requests.delete(f"http://localhost:8080/api/user/delete?user_id={user_id}", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 测试用户清理成功")
            else:
                print(f"❌ 测试用户清理失败")
        else:
            print(f"❌ 清理请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 清理测试用户异常: {e}")

def main():
    print("🚀 CNS 简单连接测试")
    print("=" * 40)
    
    # 1. 测试API连接
    if not test_api_connection():
        print("\n❌ API服务不可用，请确保CNS服务正在运行")
        return False
    
    # 2. 测试代理端口
    if not test_proxy_port():
        print("\n❌ 代理端口不可用，请检查CNS配置")
        return False
    
    # 3. 创建测试用户
    user_info = create_test_user()
    if not user_info:
        print("\n❌ 无法创建测试用户")
        return False
    
    try:
        # 4. 验证用户在数据库中
        db_user_info = verify_user_in_db(user_info['user_id'])
        if not db_user_info:
            print("\n❌ 用户未正确保存到数据库")
            return False
        
        before_upload = db_user_info['upload']
        before_download = db_user_info['download']
        
        # 5. 测试代理连接
        connection_success = test_simple_proxy_connection(
            user_info['username'], 
            user_info['password']
        )
        
        # 6. 检查流量更新
        traffic_updated = check_traffic_after_connection(
            user_info['user_id'], 
            before_upload, 
            before_download
        )
        
        # 7. 总结结果
        print(f"\n📋 测试结果:")
        print(f"   代理连接: {'✅ 成功' if connection_success else '❌ 失败'}")
        print(f"   流量更新: {'✅ 正常' if traffic_updated else '❌ 异常'}")
        
        if connection_success and traffic_updated:
            print(f"\n🎉 测试通过: 用户认证和流量统计功能正常工作")
            return True
        elif connection_success and not traffic_updated:
            print(f"\n⚠️ 部分成功: 连接正常但流量未更新")
            print(f"   可能原因:")
            print(f"   1. 流量控制未启用 (Enable_traffic_control=false)")
            print(f"   2. 流量统计保存延迟")
            print(f"   3. 认证信息未正确传递到流量统计模块")
            return False
        else:
            print(f"\n❌ 测试失败: 代理连接有问题")
            return False
    
    finally:
        # 8. 清理测试用户
        cleanup_user(user_info['user_id'])

if __name__ == "__main__":
    success = main()
    
    if not success:
        print(f"\n💡 故障排除建议:")
        print(f"1. 检查CNS服务是否正在运行")
        print(f"2. 检查配置文件中 Enable_traffic_control 是否为 true")
        print(f"3. 查看CNS服务的控制台日志输出")
        print(f"4. 运行配置检查: python3 check_config.py")
        print(f"5. 检查数据库文件权限")
        
        sys.exit(1)
    else:
        print(f"\n✅ 所有测试通过!")
        sys.exit(0)
