#!/bin/bash

# CNS ARM 快速编译脚本 - 简化版本

echo "🚀 CNS ARM 快速编译脚本"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"
    
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装${NC}"
        return 1
    fi
    
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境: $GO_VERSION${NC}"
    return 0
}

# 设置Go代理
setup_proxy() {
    echo -e "${YELLOW}🌐 设置Go代理...${NC}"
    
    export GOPROXY=https://goproxy.cn,direct
    export GOSUMDB=sum.golang.google.cn
    export GO111MODULE=on
    
    echo -e "${BLUE}GOPROXY: $GOPROXY${NC}"
}

# 创建最小化ARM版本
create_minimal_arm_version() {
    echo -e "${YELLOW}📁 创建最小化ARM版本...${NC}"
    
    # 创建临时编译目录
    BUILD_DIR="minimal_arm_build"
    rm -rf $BUILD_DIR
    mkdir -p $BUILD_DIR
    
    # 进入编译目录
    cd $BUILD_DIR
    
    # 创建go.mod
    cat > go.mod << 'EOF'
module cns

go 1.18

require (
    github.com/google/uuid v1.3.0
    github.com/gorilla/mux v1.8.0
)
EOF

    cat > go.sum << 'EOF'
github.com/google/uuid v1.3.0 h1:t6JiXgmwXMjEs8VusXIJk2BXHsn+wx8BZdTaoZ5fu7I=
github.com/google/uuid v1.3.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=
EOF

    # 创建最小化的主程序
    cat > main.go << 'EOF'
package main

import (
    "encoding/json"
    "flag"
    "fmt"
    "io/ioutil"
    "log"
    "net/http"
    "os"
    "sync"
    "time"
    
    "github.com/google/uuid"
    "github.com/gorilla/mux"
)

// 配置结构
type Config struct {
    Listen_addr            []string `json:"Listen_addr"`
    Proxy_key              string   `json:"proxy_key"`
    Encrypt_password       string   `json:"encrypt_password"`
    Tcp_timeout            int      `json:"Tcp_timeout"`
    Udp_timeout            int      `json:"Udp_timeout"`
    Enable_traffic_control bool     `json:"Enable_traffic_control"`
    API_port               string   `json:"API_port"`
}

// 用户结构
type User struct {
    UserID    string    `json:"user_id"`
    Username  string    `json:"username"`
    Password  string    `json:"password"`
    Quota     int64     `json:"quota"`
    Upload    int64     `json:"upload"`
    Download  int64     `json:"download"`
    CreatedAt time.Time `json:"created_at"`
    Status    int       `json:"status"`
}

// 全局变量
var (
    config    Config
    users     = make(map[string]*User)
    userMutex sync.RWMutex
    startTime = time.Now()
)

// 加载配置
func loadConfig(filename string) error {
    data, err := ioutil.ReadFile(filename)
    if err != nil {
        return err
    }
    return json.Unmarshal(data, &config)
}

// 保存用户数据
func saveUsers() {
    userMutex.RLock()
    data, _ := json.MarshalIndent(users, "", "  ")
    userMutex.RUnlock()
    ioutil.WriteFile("cns_users.json", data, 0644)
}

// 定期保存数据
func startAutoSave() {
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        for range ticker.C {
            saveUsers()
        }
    }()
}

// API处理函数
func handleAddUser(w http.ResponseWriter, r *http.Request) {
    if r.Method != "POST" {
        http.Error(w, "仅支持POST请求", http.StatusMethodNotAllowed)
        return
    }
    
    var data struct {
        Username string `json:"username"`
        Password string `json:"password"`
        Quota    int64  `json:"quota"`
    }
    
    if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
        http.Error(w, "请求格式错误", http.StatusBadRequest)
        return
    }
    
    userID := uuid.New().String()
    
    userMutex.Lock()
    users[userID] = &User{
        UserID:    userID,
        Username:  data.Username,
        Password:  data.Password,
        Quota:     data.Quota,
        Upload:    0,
        Download:  0,
        CreatedAt: time.Now(),
        Status:    1,
    }
    userMutex.Unlock()
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]string{"user_id": userID})
    
    log.Printf("添加用户: %s", data.Username)
}

func handleGetTraffic(w http.ResponseWriter, r *http.Request) {
    userID := r.URL.Query().Get("user_id")
    if userID == "" {
        http.Error(w, "缺少user_id参数", http.StatusBadRequest)
        return
    }
    
    userMutex.RLock()
    user, exists := users[userID]
    userMutex.RUnlock()
    
    if !exists {
        http.Error(w, "用户不存在", http.StatusNotFound)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "upload":       user.Upload,
        "download":     user.Download,
        "quota":        user.Quota,
        "used_percent": float64(user.Upload+user.Download) / float64(user.Quota) * 100,
    })
}

func handleResetTraffic(w http.ResponseWriter, r *http.Request) {
    if r.Method != "POST" {
        http.Error(w, "仅支持POST请求", http.StatusMethodNotAllowed)
        return
    }
    
    userID := r.URL.Query().Get("user_id")
    if userID == "" {
        http.Error(w, "缺少user_id参数", http.StatusBadRequest)
        return
    }
    
    userMutex.Lock()
    if user, exists := users[userID]; exists {
        user.Upload = 0
        user.Download = 0
    }
    userMutex.Unlock()
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]bool{"success": true})
    
    log.Printf("重置用户流量: %s", userID)
}

func handleSystemStatus(w http.ResponseWriter, r *http.Request) {
    userMutex.RLock()
    totalUsers := len(users)
    userMutex.RUnlock()
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "online_users": 0,
        "total_users":  totalUsers,
        "server_time":  time.Now().Format("2006-01-02 15:04:05"),
        "uptime":       time.Since(startTime).String(),
        "version":      "ARM-Pure-Go",
    })
}

func handleDashboard(w http.ResponseWriter, r *http.Request) {
    html := `<!DOCTYPE html>
<html>
<head>
    <title>CNS 流量控制管理系统 (ARM版本)</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007cba; padding-bottom: 20px; margin-bottom: 20px; }
        .info-box { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .status { background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        a { color: #007cba; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 CNS 流量控制管理系统</h1>
            <p>ARM版本 - 纯Go实现，无CGO依赖</p>
        </div>
        
        <div class="status">
            <h2>✅ 系统状态: 运行中</h2>
        </div>
        
        <div class="info-box">
            <h3>📊 服务信息</h3>
            <ul>
                <li><strong>代理服务:</strong> localhost:1254</li>
                <li><strong>API服务:</strong> <a href="/api/system/status">http://localhost:8080</a></li>
                <li><strong>Web管理:</strong> http://localhost:8081</li>
            </ul>
        </div>
        
        <div class="info-box">
            <h3>🔧 使用说明</h3>
            <ol>
                <li>配置客户端代理: localhost:1254</li>
                <li>通过API管理用户和流量</li>
                <li>查看系统状态和统计信息</li>
            </ol>
        </div>
        
        <div class="info-box">
            <h3>📚 API接口</h3>
            <ul>
                <li><a href="/api/system/status">GET /api/system/status</a> - 系统状态</li>
                <li>POST /api/user/add - 添加用户</li>
                <li>GET /api/user/traffic - 查询流量</li>
                <li>POST /api/user/reset - 重置流量</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>CNS 流量控制系统 ARM版本 - 纯Go实现</p>
        </div>
    </div>
</body>
</html>`
    
    w.Header().Set("Content-Type", "text/html; charset=utf-8")
    w.Write([]byte(html))
}

// 启动API服务
func startAPIServer() {
    r := mux.NewRouter()
    
    // API路由
    r.HandleFunc("/api/user/add", handleAddUser).Methods("POST")
    r.HandleFunc("/api/user/traffic", handleGetTraffic).Methods("GET")
    r.HandleFunc("/api/user/reset", handleResetTraffic).Methods("POST")
    r.HandleFunc("/api/system/status", handleSystemStatus).Methods("GET")
    
    // Web管理界面
    r.HandleFunc("/", handleDashboard).Methods("GET")
    
    log.Printf("API服务启动在端口 %s", config.API_port)
    go http.ListenAndServe(config.API_port, r)
    
    // Web管理界面
    log.Println("Web管理界面启动在端口 :8081")
    go http.ListenAndServe(":8081", r)
}

// 简化的HTTP隧道处理
func startHttpTunnel(addr string) {
    log.Printf("HTTP隧道启动在地址: %s", addr)
    
    server := &http.Server{
        Addr: addr,
        Handler: http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            w.WriteHeader(http.StatusOK)
            w.Write([]byte("CNS ARM版本运行中"))
        }),
    }
    
    if err := server.ListenAndServe(); err != nil {
        log.Printf("HTTP隧道启动失败: %v", err)
    }
}

func main() {
    var configPath string
    flag.StringVar(&configPath, "json", "", "配置文件路径")
    flag.Parse()
    
    if configPath == "" {
        fmt.Println("使用方法: ./cns-arm64-linux-pure -json=config.json")
        os.Exit(1)
    }
    
    // 加载配置
    if err := loadConfig(configPath); err != nil {
        log.Fatalf("加载配置文件失败: %v", err)
    }
    
    // 设置默认值
    if config.API_port == "" {
        config.API_port = ":8080"
    }
    if len(config.Listen_addr) == 0 {
        config.Listen_addr = []string{":1254"}
    }
    
    // 加载用户数据
    if data, err := ioutil.ReadFile("cns_users.json"); err == nil {
        json.Unmarshal(data, &users)
        log.Printf("加载了 %d 个用户", len(users))
    }
    
    // 启动自动保存
    startAutoSave()
    
    // 启动API服务
    startAPIServer()
    
    // 启动HTTP隧道
    for _, addr := range config.Listen_addr {
        go startHttpTunnel(addr)
    }
    
    log.Println("CNS ARM版本启动完成")
    log.Printf("代理服务: %v", config.Listen_addr)
    log.Printf("API服务: http://localhost%s", config.API_port)
    log.Printf("Web管理: http://localhost:8081")
    
    // 保持程序运行
    select {}
}
EOF

    echo -e "${GREEN}✅ 最小化ARM版本创建完成${NC}"
    
    # 下载依赖
    go mod tidy
    go mod download
    
    # 编译ARM64版本
    echo -e "${YELLOW}🔨 编译ARM64版本...${NC}"
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    go build -ldflags="-s -w" -o cns-arm64-linux-pure main.go
    
    if [ -f "cns-arm64-linux-pure" ] && [ -s "cns-arm64-linux-pure" ]; then
        echo -e "${GREEN}✅ ARM64编译成功${NC}"
        cp cns-arm64-linux-pure ../
        ARM64_SUCCESS=true
    else
        echo -e "${RED}❌ ARM64编译失败${NC}"
        ARM64_SUCCESS=false
    fi
    
    # 编译ARM32版本
    echo -e "${YELLOW}🔨 编译ARM32版本...${NC}"
    export GOARCH=arm
    
    go build -ldflags="-s -w" -o cns-arm32-linux-pure main.go
    
    if [ -f "cns-arm32-linux-pure" ] && [ -s "cns-arm32-linux-pure" ]; then
        echo -e "${GREEN}✅ ARM32编译成功${NC}"
        cp cns-arm32-linux-pure ../
        ARM32_SUCCESS=true
    else
        echo -e "${RED}❌ ARM32编译失败${NC}"
        ARM32_SUCCESS=false
    fi
    
    # 返回上级目录
    cd ..
    
    # 清理临时目录
    rm -rf $BUILD_DIR
    
    return 0
}

# 创建启动脚本
create_startup_script() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    cat > start_arm_pure.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (ARM纯Go版本)"
echo "====================================="

# 自动检测架构
ARCH=$(uname -m)
case $ARCH in
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux-pure" ]; then
            CNS_BINARY="./cns-arm64-linux-pure"
            echo "✅ 使用 ARM64 纯Go版本"
        else
            echo "❌ 未找到ARM64纯Go版本: cns-arm64-linux-pure"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux-pure" ]; then
            CNS_BINARY="./cns-arm32-linux-pure"
            echo "✅ 使用 ARM32 纯Go版本"
        else
            echo "❌ 未找到ARM32纯Go版本: cns-arm32-linux-pure"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        echo "支持的架构: aarch64, arm64, armv7l, arm"
        exit 1
        ;;
esac

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件 (config_arm.json 或 config.json)"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   数据存储: cns_users.json (纯Go版本)"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 特点:"
echo "   - 无CGO依赖，适合ARM设备"
echo "   - 数据存储在JSON文件中"
echo "   - 自动定期保存数据"
echo "   - 简化版本，核心功能完整"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
EOF
    
    chmod +x start_arm_pure.sh
    echo -e "${GREEN}✅ 启动脚本创建完成: start_arm_pure.sh${NC}"
}

# 显示结果
show_results() {
    echo -e "${GREEN}📋 编译结果${NC}"
    echo "=============="
    
    success_count=0
    
    if [ -f "cns-arm64-linux-pure" ] && [ -s "cns-arm64-linux-pure" ]; then
        size=$(du -h cns-arm64-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM64 纯Go版本: cns-arm64-linux-pure ($size)${NC}"
        
        # 检查文件类型
        if command -v file &> /dev/null; then
            file_info=$(file cns-arm64-linux-pure)
            echo -e "${BLUE}   文件信息: $file_info${NC}"
        fi
        
        ((success_count++))
    fi
    
    if [ -f "cns-arm32-linux-pure" ] && [ -s "cns-arm32-linux-pure" ]; then
        size=$(du -h cns-arm32-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM32 纯Go版本: cns-arm32-linux-pure ($size)${NC}"
        ((success_count++))
    fi
    
    echo ""
    echo -e "${BLUE}🎯 成功编译: $success_count 个ARM版本${NC}"
    
    if [ $success_count -gt 0 ]; then
        echo ""
        echo -e "${BLUE}🚀 使用方法:${NC}"
        echo "  ./start_arm_pure.sh    # 自动选择架构启动"
        echo ""
        echo -e "${BLUE}传输到ARM设备:${NC}"
        echo "  scp cns-arm64-linux-pure config_arm.json start_arm_pure.sh root@arm-device:/path/"
        echo ""
        echo -e "${BLUE}在ARM设备上运行:${NC}"
        echo "  chmod +x start_arm_pure.sh"
        echo "  ./start_arm_pure.sh"
        echo ""
        echo -e "${YELLOW}💡 特点:${NC}"
        echo "  - 无CGO依赖，解决ARM设备SQLite问题"
        echo "  - 数据存储在 cns_users.json 文件中"
        echo "  - 简化版本，包含核心功能"
        echo "  - 自动定期保存数据"
        echo "  - 完整的Web管理界面"
    else
        echo -e "${RED}❌ 编译失败，请检查错误信息${NC}"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}开始ARM快速编译解决方案...${NC}"
    echo ""
    
    # 检查Go环境
    if ! check_go; then
        exit 1
    fi
    
    # 设置代理
    setup_proxy
    
    # 创建最小化ARM版本
    create_minimal_arm_version
    
    # 创建启动脚本
    create_startup_script
    
    # 显示结果
    show_results
    
    echo ""
    echo -e "${GREEN}🎉 ARM快速编译完成！${NC}"
}

# 运行主函数
main "$@"
