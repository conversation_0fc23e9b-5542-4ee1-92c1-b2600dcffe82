package main

import (
    "encoding/json"
    "log"
    "net/http"
    "strconv"
    "time"
    "github.com/google/uuid"
    "github.com/gorilla/mux"
)

// 启动API服务
func startAPIServer() {
    r := mux.NewRouter()

    // 用户管理API
    r.HandleFunc("/api/user/add", handleAddUser).Methods("POST")
    r.<PERSON>le<PERSON>un<PERSON>("/api/user/delete", handleDeleteUser).Methods("DELETE")
    r.HandleFunc("/api/user/reset", handleResetTraffic).Methods("POST")
    r.HandleFunc("/api/user/traffic", handleGetTraffic).Methods("GET")
    r.<PERSON>leFunc("/api/user/bind", handleBindWechat).Methods("POST")
    r.HandleFunc("/api/user/info/{userID}", handleGetUserInfo).Methods("GET")
    r.HandleFunc("/api/user/update/{userID}", handleUpdateUser).Methods("PUT")
    r.<PERSON>le<PERSON>un<PERSON>("/api/user/expire/{userID}", handleSetExpire).Methods("POST")

    // 流量统计API
    r.HandleFunc("/api/stats/realtime/{userID}", handleRealtimeStats).Methods("GET")
    r.HandleFunc("/api/stats/daily/{userID}", handleDailyStats).Methods("GET")
    r.HandleFunc("/api/stats/history/{userID}", handleConnectionHistory).Methods("GET")

    // 系统监控API
    r.HandleFunc("/api/system/status", handleSystemStatus).Methods("GET")
    r.HandleFunc("/api/system/users", handleListUsers).Methods("GET")

    // 认证API
    r.HandleFunc("/api/auth/login", handleUserLogin).Methods("POST")

    log.Println("API服务启动在 :8080 端口")
    go http.ListenAndServe(":8080", r)
}

// 处理添加用户请求
func handleAddUser(w http.ResponseWriter, r *http.Request) {
    if r.Method != "POST" {
        http.Error(w, "仅支持POST请求", http.StatusMethodNotAllowed)
        return
    }
    
    var data struct {
        Username string `json:"username"`
        Password string `json:"password"`
        Quota    int64  `json:"quota"`
    }
    
    if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
        http.Error(w, "请求格式错误", http.StatusBadRequest)
        return
    }
    
    userID := uuid.New().String()
    if err := addUser(userID, data.Username, data.Password, data.Quota); err != nil {
        http.Error(w, "添加用户失败: "+err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]string{"user_id": userID})
}

// 处理删除用户请求
func handleDeleteUser(w http.ResponseWriter, r *http.Request) {
    userID := r.URL.Query().Get("user_id")
    if userID == "" {
        http.Error(w, "缺少user_id参数", http.StatusBadRequest)
        return
    }
    
    if err := deleteUser(userID); err != nil {
        http.Error(w, "删除用户失败: "+err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// 处理重置流量请求
func handleResetTraffic(w http.ResponseWriter, r *http.Request) {
    userID := r.URL.Query().Get("user_id")
    if userID == "" {
        http.Error(w, "缺少user_id参数", http.StatusBadRequest)
        return
    }
    
    if err := resetUserTraffic(userID); err != nil {
        http.Error(w, "重置流量失败: "+err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// 处理获取流量请求
func handleGetTraffic(w http.ResponseWriter, r *http.Request) {
    userID := r.URL.Query().Get("user_id")
    if userID == "" {
        http.Error(w, "缺少user_id参数", http.StatusBadRequest)
        return
    }
    
    upload, download, quota, err := getUserTraffic(userID)
    if err != nil {
        http.Error(w, "获取流量信息失败: "+err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "upload": upload,
        "download": download,
        "quota": quota,
        "used_percent": float64(upload+download) / float64(quota) * 100,
    })
}

// 处理绑定微信ID请求
func handleBindWechat(w http.ResponseWriter, r *http.Request) {
    var data struct {
        UserID   string `json:"user_id"`
        WechatID string `json:"wechat_id"`
    }

    if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
        http.Error(w, "请求格式错误", http.StatusBadRequest)
        return
    }

    if err := bindWechatID(data.UserID, data.WechatID); err != nil {
        http.Error(w, "绑定微信ID失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// 处理用户登录请求
func handleUserLogin(w http.ResponseWriter, r *http.Request) {
    var data struct {
        Username string `json:"username"`
        Password string `json:"password"`
    }

    if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
        http.Error(w, "请求格式错误", http.StatusBadRequest)
        return
    }

    user, err := authenticateUser(data.Username, data.Password)
    if err != nil {
        http.Error(w, "登录失败: "+err.Error(), http.StatusUnauthorized)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "user_id": user.UserID,
        "username": user.Username,
        "quota": user.Quota,
        "upload": user.Upload,
        "download": user.Download,
        "expire_at": user.ExpireAt,
    })
}

// 处理获取用户信息请求
func handleGetUserInfo(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]

    user, err := getUserByID(userID)
    if err != nil {
        http.Error(w, "获取用户信息失败: "+err.Error(), http.StatusNotFound)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "user_id": user.UserID,
        "username": user.Username,
        "quota": user.Quota,
        "upload": user.Upload,
        "download": user.Download,
        "expire_at": user.ExpireAt,
        "status": user.Status,
        "wechat_id": user.WechatID,
    })
}

// 处理更新用户信息请求
func handleUpdateUser(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]

    var data struct {
        Quota  *int64  `json:"quota,omitempty"`
        Status *int    `json:"status,omitempty"`
    }

    if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
        http.Error(w, "请求格式错误", http.StatusBadRequest)
        return
    }

    if err := updateUserInfo(userID, data.Quota, data.Status); err != nil {
        http.Error(w, "更新用户信息失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// 处理设置用户过期时间请求
func handleSetExpire(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]

    var data struct {
        ExpireAt string `json:"expire_at"` // 格式: "2024-12-31 23:59:59"
    }

    if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
        http.Error(w, "请求格式错误", http.StatusBadRequest)
        return
    }

    expireTime, err := time.Parse("2006-01-02 15:04:05", data.ExpireAt)
    if err != nil {
        http.Error(w, "时间格式错误", http.StatusBadRequest)
        return
    }

    if err := setUserExpire(userID, expireTime); err != nil {
        http.Error(w, "设置过期时间失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// 处理实时流量统计请求
func handleRealtimeStats(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]

    upload, download := getUserTrafficStats(userID)
    dbUpload, dbDownload, quota, err := getUserTraffic(userID)
    if err != nil {
        http.Error(w, "获取流量信息失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    totalUpload := dbUpload + upload
    totalDownload := dbDownload + download

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "realtime_upload": upload,
        "realtime_download": download,
        "total_upload": totalUpload,
        "total_download": totalDownload,
        "quota": quota,
        "used_percent": float64(totalUpload+totalDownload) / float64(quota) * 100,
        "remaining": quota - (totalUpload + totalDownload),
    })
}

// 处理每日流量统计请求
func handleDailyStats(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]

    daysStr := r.URL.Query().Get("days")
    days := 7 // 默认7天
    if daysStr != "" {
        if d, err := strconv.Atoi(daysStr); err == nil && d > 0 {
            days = d
        }
    }

    stats, err := getUserDailyStats(userID, days)
    if err != nil {
        http.Error(w, "获取每日统计失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "stats": stats,
        "days": days,
    })
}

// 处理连接历史请求
func handleConnectionHistory(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]

    limitStr := r.URL.Query().Get("limit")
    limit := 50 // 默认50条
    if limitStr != "" {
        if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
            limit = l
        }
    }

    history, err := getUserConnectionHistory(userID, limit)
    if err != nil {
        http.Error(w, "获取连接历史失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "history": history,
        "limit": limit,
    })
}

// 处理系统状态请求
func handleSystemStatus(w http.ResponseWriter, r *http.Request) {
    // 获取在线用户数
    trafficMutex.RLock()
    onlineUsers := len(userTrafficStats)
    trafficMutex.RUnlock()

    // 获取总用户数
    totalUsers, err := getTotalUserCount()
    if err != nil {
        log.Printf("获取总用户数失败: %v", err)
        totalUsers = 0
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "online_users": onlineUsers,
        "total_users": totalUsers,
        "server_time": time.Now().Format("2006-01-02 15:04:05"),
        "uptime": time.Since(startTime).String(),
    })
}

// 处理用户列表请求
func handleListUsers(w http.ResponseWriter, r *http.Request) {
    pageStr := r.URL.Query().Get("page")
    limitStr := r.URL.Query().Get("limit")

    page := 1
    limit := 20

    if pageStr != "" {
        if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
            page = p
        }
    }

    if limitStr != "" {
        if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
            limit = l
        }
    }

    users, total, err := getUserList(page, limit)
    if err != nil {
        http.Error(w, "获取用户列表失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "users": users,
        "total": total,
        "page": page,
        "limit": limit,
        "total_pages": (total + limit - 1) / limit,
    })
}