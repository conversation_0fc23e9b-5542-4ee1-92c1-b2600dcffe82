package main

import (
    "encoding/json"
    "html/template"
    "net/http"
    "strconv"
    "github.com/gorilla/mux"
)

// 启动Web管理界面
func startWebAdmin() {
    r := mux.NewRouter()
    
    // 静态文件服务
    r.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("./static/"))))
    
    // Web页面路由
    r.HandleFunc("/", handleDashboard).Methods("GET")
    r.HandleFunc("/users", handleUsersPage).Methods("GET")
    r.HandleFunc("/stats", handleStatsPage).Methods("GET")
    
    // Web API路由
    r.HandleFunc("/web/api/users", handleWebAPIUsers).Methods("GET")
    r.HandleFunc("/web/api/user/{userID}/stats", handleWebAPIUserStats).Methods("GET")
    r.<PERSON><PERSON>un<PERSON>("/web/api/system/status", handleWebAPISystemStatus).Methods("GET")
    
    go http.ListenAndServe(":8081", r)
}

// 仪表板页面
func handleDashboard(w http.ResponseWriter, r *http.Request) {
    tmpl := `
<!DOCTYPE html>
<html>
<head>
    <title>CNS 流量控制管理系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .nav { margin: 20px 0; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007cba; }
        .card { background: white; border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
        .stats { display: flex; gap: 20px; }
        .stat-item { flex: 1; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007cba; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CNS 流量控制管理系统</h1>
        <p>网络代理服务器流量管理与监控</p>
    </div>
    
    <div class="nav">
        <a href="/">仪表板</a>
        <a href="/users">用户管理</a>
        <a href="/stats">流量统计</a>
    </div>
    
    <div class="card">
        <h2>系统状态</h2>
        <div class="stats" id="systemStats">
            <div class="stat-item">
                <div class="stat-number" id="onlineUsers">-</div>
                <div>在线用户</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalUsers">-</div>
                <div>总用户数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="serverUptime">-</div>
                <div>运行时间</div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h2>最近用户</h2>
        <div id="recentUsers">加载中...</div>
    </div>
    
    <script>
        function loadSystemStatus() {
            fetch('/web/api/system/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('onlineUsers').textContent = data.online_users;
                    document.getElementById('totalUsers').textContent = data.total_users;
                    document.getElementById('serverUptime').textContent = data.uptime;
                });
        }
        
        function loadRecentUsers() {
            fetch('/web/api/users?limit=5')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('recentUsers');
                    if (data.users && data.users.length > 0) {
                        let html = '<table border="1" style="width:100%; border-collapse: collapse;">';
                        html += '<tr><th>用户名</th><th>配额</th><th>已用流量</th><th>使用率</th><th>状态</th></tr>';
                        data.users.forEach(user => {
                            const usedGB = ((user.upload + user.download) / 1024 / 1024 / 1024).toFixed(2);
                            const quotaGB = (user.quota / 1024 / 1024 / 1024).toFixed(2);
                            const percent = user.used_percent.toFixed(1);
                            const status = user.status === 1 ? '正常' : '禁用';
                            html += '<tr>' +
                                '<td>' + user.username + '</td>' +
                                '<td>' + quotaGB + 'GB</td>' +
                                '<td>' + usedGB + 'GB</td>' +
                                '<td>' + percent + '%</td>' +
                                '<td>' + status + '</td>' +
                            '</tr>';
                        });
                        html += '</table>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '暂无用户数据';
                    }
                });
        }
        
        // 页面加载时执行
        loadSystemStatus();
        loadRecentUsers();
        
        // 每30秒刷新一次
        setInterval(loadSystemStatus, 30000);
        setInterval(loadRecentUsers, 60000);
    </script>
</body>
</html>`
    
    w.Header().Set("Content-Type", "text/html; charset=utf-8")
    w.Write([]byte(tmpl))
}

// 用户管理页面
func handleUsersPage(w http.ResponseWriter, r *http.Request) {
    tmpl := `
<!DOCTYPE html>
<html>
<head>
    <title>用户管理 - CNS 流量控制管理系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .nav { margin: 20px 0; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007cba; }
        .card { background: white; border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 5px 10px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007cba; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>用户管理</h1>
    </div>
    
    <div class="nav">
        <a href="/">仪表板</a>
        <a href="/users">用户管理</a>
        <a href="/stats">流量统计</a>
    </div>
    
    <div class="card">
        <h2>用户列表</h2>
        <div id="usersList">加载中...</div>
    </div>
    
    <script>
        function loadUsers() {
            fetch('/web/api/users')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('usersList');
                    if (data.users && data.users.length > 0) {
                        let html = '<table>';
                        html += '<tr><th>用户名</th><th>配额</th><th>上传</th><th>下载</th><th>使用率</th><th>创建时间</th><th>过期时间</th><th>状态</th><th>操作</th></tr>';
                        data.users.forEach(user => {
                            const uploadGB = (user.upload / 1024 / 1024 / 1024).toFixed(2);
                            const downloadGB = (user.download / 1024 / 1024 / 1024).toFixed(2);
                            const quotaGB = (user.quota / 1024 / 1024 / 1024).toFixed(2);
                            const percent = user.used_percent.toFixed(1);
                            const status = user.status === 1 ? '正常' : '禁用';
                            const expireAt = user.expire_at || '永不过期';
                            html += '<tr>' +
                                '<td>' + user.username + '</td>' +
                                '<td>' + quotaGB + 'GB</td>' +
                                '<td>' + uploadGB + 'GB</td>' +
                                '<td>' + downloadGB + 'GB</td>' +
                                '<td>' + percent + '%</td>' +
                                '<td>' + user.created_at + '</td>' +
                                '<td>' + expireAt + '</td>' +
                                '<td>' + status + '</td>' +
                                '<td>' +
                                    '<button class="btn btn-primary" onclick="viewUserStats(\'' + user.user_id + '\')">查看统计</button>' +
                                    '<button class="btn btn-danger" onclick="resetUserTraffic(\'' + user.user_id + '\')">重置流量</button>' +
                                '</td>' +
                            '</tr>';
                        });
                        html += '</table>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '暂无用户数据';
                    }
                });
        }
        
        function viewUserStats(userID) {
            window.open('/stats?user_id=' + userID, '_blank');
        }

        function resetUserTraffic(userID) {
            if (confirm('确定要重置该用户的流量吗？')) {
                fetch('/api/user/reset?user_id=' + userID, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('流量重置成功');
                            loadUsers();
                        } else {
                            alert('流量重置失败');
                        }
                    });
            }
        }
        
        loadUsers();
    </script>
</body>
</html>`
    
    w.Header().Set("Content-Type", "text/html; charset=utf-8")
    w.Write([]byte(tmpl))
}

// 流量统计页面
func handleStatsPage(w http.ResponseWriter, r *http.Request) {
    userID := r.URL.Query().Get("user_id")
    
    tmpl := template.Must(template.New("stats").Parse(`
<!DOCTYPE html>
<html>
<head>
    <title>流量统计 - CNS 流量控制管理系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .nav { margin: 20px 0; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007cba; }
        .card { background: white; border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>流量统计</h1>
    </div>
    
    <div class="nav">
        <a href="/">仪表板</a>
        <a href="/users">用户管理</a>
        <a href="/stats">流量统计</a>
    </div>
    
    <div class="card">
        <h2>用户流量统计</h2>
        {{if .UserID}}
        <p>用户ID: {{.UserID}}</p>
        <div id="userStats">加载中...</div>
        {{else}}
        <p>请从用户管理页面选择用户查看统计</p>
        {{end}}
    </div>
    
    <script>
        {{if .UserID}}
        function loadUserStats() {
            fetch('/web/api/user/{{.UserID}}/stats')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('userStats');
                    let html = '<h3>实时统计</h3>';
                    html += '<p>实时上传: ' + (data.realtime.realtime_upload / 1024 / 1024).toFixed(2) + ' MB</p>';
                    html += '<p>实时下载: ' + (data.realtime.realtime_download / 1024 / 1024).toFixed(2) + ' MB</p>';
                    html += '<p>总上传: ' + (data.realtime.total_upload / 1024 / 1024 / 1024).toFixed(2) + ' GB</p>';
                    html += '<p>总下载: ' + (data.realtime.total_download / 1024 / 1024 / 1024).toFixed(2) + ' GB</p>';
                    html += '<p>配额: ' + (data.realtime.quota / 1024 / 1024 / 1024).toFixed(2) + ' GB</p>';
                    html += '<p>使用率: ' + data.realtime.used_percent.toFixed(1) + '%</p>';
                    
                    if (data.daily && data.daily.length > 0) {
                        html += '<h3>每日统计</h3>';
                        html += '<table border="1" style="width:100%; border-collapse: collapse;">';
                        html += '<tr><th>日期</th><th>上传</th><th>下载</th><th>总计</th><th>连接数</th></tr>';
                        data.daily.forEach(day => {
                            const uploadGB = (day.upload / 1024 / 1024 / 1024).toFixed(2);
                            const downloadGB = (day.download / 1024 / 1024 / 1024).toFixed(2);
                            const totalGB = (day.total / 1024 / 1024 / 1024).toFixed(2);
                            html += '<tr>' +
                                '<td>' + day.date + '</td>' +
                                '<td>' + uploadGB + 'GB</td>' +
                                '<td>' + downloadGB + 'GB</td>' +
                                '<td>' + totalGB + 'GB</td>' +
                                '<td>' + day.connections + '</td>' +
                            '</tr>';
                        });
                        html += '</table>';
                    }
                    
                    container.innerHTML = html;
                });
        }
        
        loadUserStats();
        setInterval(loadUserStats, 30000);
        {{end}}
    </script>
</body>
</html>`))
    
    data := struct {
        UserID string
    }{
        UserID: userID,
    }
    
    w.Header().Set("Content-Type", "text/html; charset=utf-8")
    tmpl.Execute(w, data)
}

// Web API处理函数
func handleWebAPIUsers(w http.ResponseWriter, r *http.Request) {
    limitStr := r.URL.Query().Get("limit")
    limit := 20
    if limitStr != "" {
        if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
            limit = l
        }
    }
    
    users, total, err := getUserList(1, limit)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "users": users,
        "total": total,
    })
}

func handleWebAPIUserStats(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    userID := vars["userID"]
    
    // 获取实时统计
    upload, download := getUserTrafficStats(userID)
    dbUpload, dbDownload, quota, err := getUserTraffic(userID)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    totalUpload := dbUpload + upload
    totalDownload := dbDownload + download
    
    realtime := map[string]interface{}{
        "realtime_upload": upload,
        "realtime_download": download,
        "total_upload": totalUpload,
        "total_download": totalDownload,
        "quota": quota,
        "used_percent": float64(totalUpload+totalDownload) / float64(quota) * 100,
    }
    
    // 获取每日统计
    daily, err := getUserDailyStats(userID, 7)
    if err != nil {
        daily = []map[string]interface{}{}
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "realtime": realtime,
        "daily": daily,
    })
}

func handleWebAPISystemStatus(w http.ResponseWriter, r *http.Request) {
    handleSystemStatus(w, r)
}
