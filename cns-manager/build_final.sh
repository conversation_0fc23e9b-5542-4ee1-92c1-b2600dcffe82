#!/bin/bash

# CNS 最终编译脚本

echo "🚀 CNS 最终编译脚本"
echo "=================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查并修复常见问题
check_and_fix() {
    echo -e "${YELLOW}🔍 检查常见问题...${NC}"
    
    # 检查go.mod版本
    if grep -q "go 1.19" go.mod; then
        echo -e "${BLUE}修复go.mod版本...${NC}"
        sed -i 's/go 1.19/go 1.18/g' go.mod
    fi
    
    # 检查time包导入
    if ! grep -q "\"time\"" db.go; then
        echo -e "${BLUE}修复db.go中的time包导入...${NC}"
        sed -i '/import (/a\    "time"' db.go
    fi
    
    # 检查go.sum文件
    if [ ! -f "go.sum" ] || [ ! -s "go.sum" ]; then
        echo -e "${BLUE}创建go.sum文件...${NC}"
        cat > go.sum << 'EOF'
github.com/google/uuid v1.3.0 h1:t6JiXgmwXMjEs8VusXIJk2BXHsn+wx8BZdTaoZ5fu7I=
github.com/google/uuid v1.3.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=
github.com/mattn/go-sqlite3 v1.14.17 h1:mCRHCLDUBXgpKAqIKsaAaAsrAlbkeomtRFKXh2L6YIM=
github.com/mattn/go-sqlite3 v1.14.17/go.mod h1:2eHXhiwb8IkHr+BDWZGa96P6+rkvnG63S2DGjv9HUNg=
EOF
    fi
    
    echo -e "${GREEN}✅ 问题检查完成${NC}"
}

# 编译ARM64版本
build_arm64() {
    echo -e "${YELLOW}🔨 编译ARM64版本...${NC}"
    
    # 设置环境变量
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    # 编译
    go build -ldflags="-s -w" -o cns-arm64-linux
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM64编译成功: cns-arm64-linux${NC}"
        
        # 显示文件信息
        if [ -f "cns-arm64-linux" ]; then
            file_size=$(du -h cns-arm64-linux | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"
            
            if command -v file &> /dev/null; then
                file_type=$(file cns-arm64-linux)
                echo -e "${BLUE}📋 文件类型: $file_type${NC}"
            fi
        fi
        return 0
    else
        echo -e "${RED}❌ ARM64编译失败${NC}"
        return 1
    fi
}

# 编译ARM32版本
build_arm32() {
    echo -e "${YELLOW}🔨 编译ARM32版本...${NC}"
    
    # 设置环境变量
    export GOOS=linux
    export GOARCH=arm
    export CGO_ENABLED=0
    
    # 编译
    go build -ldflags="-s -w" -o cns-arm32-linux
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ARM32编译成功: cns-arm32-linux${NC}"
        
        # 显示文件信息
        if [ -f "cns-arm32-linux" ]; then
            file_size=$(du -h cns-arm32-linux | cut -f1)
            echo -e "${BLUE}📊 文件大小: $file_size${NC}"
        fi
        return 0
    else
        echo -e "${RED}❌ ARM32编译失败${NC}"
        return 1
    fi
}

# 创建启动脚本
create_startup_scripts() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    # Linux启动脚本
    cat > start_cns.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统"
echo "========================"

# 检查可执行文件
if [ -f "cns-arm64-linux" ]; then
    CNS_BINARY="./cns-arm64-linux"
    echo "使用 ARM64 版本"
elif [ -f "cns-arm32-linux" ]; then
    CNS_BINARY="./cns-arm32-linux"
    echo "使用 ARM32 版本"
else
    echo "❌ 未找到CNS可执行文件"
    echo "请先编译: ./build_final.sh"
    exit 1
fi

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
EOF

    chmod +x start_cns.sh
    
    # 后台启动脚本
    cat > start_cns_daemon.sh << 'EOF'
#!/bin/bash

echo "🚀 后台启动 CNS 流量控制系统"
echo "============================"

# 检查可执行文件
if [ -f "cns-arm64-linux" ]; then
    CNS_BINARY="./cns-arm64-linux"
elif [ -f "cns-arm32-linux" ]; then
    CNS_BINARY="./cns-arm32-linux"
else
    echo "❌ 未找到CNS可执行文件"
    exit 1
fi

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件"
    exit 1
fi

# 检查是否已经在运行
if pgrep -f "cns.*json" > /dev/null; then
    echo "⚠️  CNS服务已在运行"
    echo "如需重启，请先运行: ./stop_cns.sh"
    exit 1
fi

# 后台启动
nohup $CNS_BINARY -json=$CONFIG_FILE > cns.log 2>&1 &
PID=$!

echo "✅ CNS服务已后台启动"
echo "📊 进程ID: $PID"
echo "📝 日志文件: cns.log"
echo ""
echo "查看日志: tail -f cns.log"
echo "停止服务: ./stop_cns.sh"
EOF

    chmod +x start_cns_daemon.sh
    
    # 停止脚本
    cat > stop_cns.sh << 'EOF'
#!/bin/bash

echo "🛑 停止 CNS 服务"
echo "==============="

# 查找CNS进程
PIDS=$(pgrep -f "cns.*json")

if [ -z "$PIDS" ]; then
    echo "❌ 未找到运行中的CNS服务"
    exit 1
fi

# 停止进程
for PID in $PIDS; do
    echo "停止进程 $PID..."
    kill $PID
    
    # 等待进程结束
    sleep 2
    
    # 如果进程仍在运行，强制杀死
    if kill -0 $PID 2>/dev/null; then
        echo "强制停止进程 $PID..."
        kill -9 $PID
    fi
done

echo "✅ CNS服务已停止"
EOF

    chmod +x stop_cns.sh
    
    echo -e "${GREEN}✅ 启动脚本创建完成${NC}"
}

# 创建配置文件
create_config() {
    if [ ! -f "config_arm.json" ]; then
        echo -e "${YELLOW}⚙️ 创建ARM配置文件...${NC}"
        
        cat > config_arm.json << 'EOF'
{
    "Tcp_timeout": 300,
    "Udp_timeout": 30,
    "Listen_addr": [":1254"],
    "proxy_key": "Host",
    "encrypt_password": "password",
    "Enable_dns_tcpOverUdp": true,
    "Enable_httpDNS": true,
    "Enable_TFO": false,
    "Enable_traffic_control": true,
    "API_port": ":8080",
    "Tls": {
        "listen_addr": [":8978"],
        "AutoCertHosts": ["localhost"]
    }
}
EOF
        
        echo -e "${GREEN}✅ ARM配置文件已创建: config_arm.json${NC}"
    fi
}

# 显示使用说明
show_usage() {
    echo -e "${GREEN}📋 编译完成！${NC}"
    echo ""
    echo -e "${BLUE}🚀 使用方法:${NC}"
    echo ""
    echo -e "${YELLOW}前台运行:${NC}"
    echo "  ./start_cns.sh"
    echo ""
    echo -e "${YELLOW}后台运行:${NC}"
    echo "  ./start_cns_daemon.sh"
    echo "  tail -f cns.log          # 查看日志"
    echo "  ./stop_cns.sh            # 停止服务"
    echo ""
    echo -e "${YELLOW}手动运行:${NC}"
    if [ -f "cns-arm64-linux" ]; then
        echo "  ./cns-arm64-linux -json=config_arm.json"
    fi
    if [ -f "cns-arm32-linux" ]; then
        echo "  ./cns-arm32-linux -json=config_arm.json"
    fi
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  代理服务: localhost:1254"
    echo "  API服务: http://localhost:8080"
    echo "  Web管理: http://localhost:8081"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "  - 确保防火墙允许相应端口"
    echo "  - 建议使用非root用户运行"
    echo "  - 查看详细文档: TRAFFIC_CONTROL_README.md"
}

# 主函数
main() {
    echo -e "${BLUE}开始最终编译流程...${NC}"
    echo ""
    
    # 检查并修复问题
    check_and_fix
    
    # 创建配置文件
    create_config
    
    # 编译ARM版本
    success_count=0
    
    if build_arm64; then
        ((success_count++))
    fi
    
    if build_arm32; then
        ((success_count++))
    fi
    
    if [ $success_count -eq 0 ]; then
        echo -e "${RED}❌ 所有编译都失败了${NC}"
        echo ""
        echo -e "${YELLOW}请检查以下问题:${NC}"
        echo "1. Go版本是否为1.18+"
        echo "2. 网络连接是否正常"
        echo "3. 依赖包是否正确下载"
        exit 1
    fi
    
    # 创建启动脚本
    create_startup_scripts
    
    # 显示使用说明
    show_usage
    
    echo -e "${GREEN}🎉 编译完成！${NC}"
}

# 运行主函数
main "$@"
