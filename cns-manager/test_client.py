#!/usr/bin/env python3
"""
CNS 客户端测试脚本
测试用户认证和流量统计功能
"""

import requests
import json
import time
import base64
import socket

class CNSTestClient:
    def __init__(self, api_base_url="http://localhost:8080", proxy_host="localhost", proxy_port=1254):
        self.api_base_url = api_base_url
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        self.session = requests.Session()
    
    def create_test_user(self):
        """创建测试用户"""
        print("👤 创建测试用户...")
        
        username = f"test_{int(time.time())}"
        password = "test123"
        quota = 100 * 1024 * 1024  # 100MB
        
        try:
            data = {
                "username": username,
                "password": password,
                "quota": quota
            }
            
            response = self.session.post(f"{self.api_base_url}/api/user/add", json=data)
            response.raise_for_status()
            result = response.json()
            
            user_id = result['user_id']
            print(f"✅ 测试用户创建成功: {username} (ID: {user_id})")
            
            return {
                'user_id': user_id,
                'username': username,
                'password': password,
                'quota': quota
            }
        except Exception as e:
            print(f"❌ 创建测试用户失败: {e}")
            return None
    
    def test_proxy_with_basic_auth(self, user_info):
        """测试Basic认证的代理连接"""
        print(f"\n🔗 测试Basic认证代理连接...")
        
        username = user_info['username']
        password = user_info['password']
        
        # 创建Basic认证字符串
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        
        try:
            # 使用socket直接连接代理
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((self.proxy_host, self.proxy_port))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT httpbin.org:80 HTTP/1.1\r\n"
            connect_request += f"Host: httpbin.org:80\r\n"
            connect_request += f"Authorization: Basic {auth_string}\r\n"
            connect_request += f"Proxy-Connection: keep-alive\r\n"
            connect_request += f"\r\n"
            
            print(f"📤 发送CONNECT请求:")
            print(f"   目标: httpbin.org:80")
            print(f"   认证: Basic {auth_string}")
            
            sock.send(connect_request.encode())
            response = sock.recv(1024).decode()
            
            print(f"📥 代理响应: {response.strip()}")
            
            if "200 Connection established" in response:
                print(f"✅ 代理连接建立成功")
                
                # 发送HTTP请求
                http_request = "GET /get HTTP/1.1\r\n"
                http_request += "Host: httpbin.org\r\n"
                http_request += "User-Agent: CNS-Test-Client\r\n"
                http_request += "Connection: close\r\n"
                http_request += "\r\n"
                
                sock.send(http_request.encode())
                http_response = sock.recv(4096)
                
                print(f"📊 HTTP响应大小: {len(http_response)} bytes")
                sock.close()
                return True
            else:
                print(f"❌ 代理连接失败")
                sock.close()
                return False
                
        except Exception as e:
            print(f"❌ 代理连接异常: {e}")
            return False
    
    def test_proxy_with_user_id(self, user_info):
        """测试User-ID头的代理连接"""
        print(f"\n🔗 测试User-ID头代理连接...")
        
        user_id = user_info['user_id']
        
        try:
            # 使用socket直接连接代理
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((self.proxy_host, self.proxy_port))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT httpbin.org:80 HTTP/1.1\r\n"
            connect_request += f"Host: httpbin.org:80\r\n"
            connect_request += f"User-ID: {user_id}\r\n"
            connect_request += f"Proxy-Connection: keep-alive\r\n"
            connect_request += f"\r\n"
            
            print(f"📤 发送CONNECT请求:")
            print(f"   目标: httpbin.org:80")
            print(f"   用户ID: {user_id}")
            
            sock.send(connect_request.encode())
            response = sock.recv(1024).decode()
            
            print(f"📥 代理响应: {response.strip()}")
            
            if "200 Connection established" in response:
                print(f"✅ 代理连接建立成功")
                
                # 发送HTTP请求
                http_request = "GET /get HTTP/1.1\r\n"
                http_request += "Host: httpbin.org\r\n"
                http_request += "User-Agent: CNS-Test-Client\r\n"
                http_request += "Connection: close\r\n"
                http_request += "\r\n"
                
                sock.send(http_request.encode())
                http_response = sock.recv(4096)
                
                print(f"📊 HTTP响应大小: {len(http_response)} bytes")
                sock.close()
                return True
            else:
                print(f"❌ 代理连接失败")
                sock.close()
                return False
                
        except Exception as e:
            print(f"❌ 代理连接异常: {e}")
            return False
    
    def check_traffic_update(self, user_id, before_stats=None):
        """检查流量更新"""
        print(f"\n📊 检查流量更新...")
        
        try:
            # 获取用户信息
            response = self.session.get(f"{self.api_base_url}/api/user/info/{user_id}")
            response.raise_for_status()
            user_info = response.json()
            
            # 获取实时统计
            response = self.session.get(f"{self.api_base_url}/api/stats/realtime/{user_id}")
            response.raise_for_status()
            realtime_info = response.json()
            
            current_stats = {
                'db_upload': user_info['upload'],
                'db_download': user_info['download'],
                'realtime_upload': realtime_info['realtime_upload'],
                'realtime_download': realtime_info['realtime_download'],
                'total_upload': realtime_info['total_upload'],
                'total_download': realtime_info['total_download']
            }
            
            print(f"📈 当前流量统计:")
            print(f"   数据库上传: {current_stats['db_upload']} bytes")
            print(f"   数据库下载: {current_stats['db_download']} bytes")
            print(f"   实时上传: {current_stats['realtime_upload']} bytes")
            print(f"   实时下载: {current_stats['realtime_download']} bytes")
            print(f"   总上传: {current_stats['total_upload']} bytes")
            print(f"   总下载: {current_stats['total_download']} bytes")
            
            if before_stats:
                print(f"\n📊 流量变化:")
                db_upload_change = current_stats['db_upload'] - before_stats['db_upload']
                db_download_change = current_stats['db_download'] - before_stats['db_download']
                realtime_upload_change = current_stats['realtime_upload'] - before_stats['realtime_upload']
                realtime_download_change = current_stats['realtime_download'] - before_stats['realtime_download']
                total_upload_change = current_stats['total_upload'] - before_stats['total_upload']
                total_download_change = current_stats['total_download'] - before_stats['total_download']
                
                print(f"   数据库上传变化: +{db_upload_change} bytes")
                print(f"   数据库下载变化: +{db_download_change} bytes")
                print(f"   实时上传变化: +{realtime_upload_change} bytes")
                print(f"   实时下载变化: +{realtime_download_change} bytes")
                print(f"   总上传变化: +{total_upload_change} bytes")
                print(f"   总下载变化: +{total_download_change} bytes")
                
                has_change = any([
                    db_upload_change > 0, db_download_change > 0,
                    realtime_upload_change > 0, realtime_download_change > 0,
                    total_upload_change > 0, total_download_change > 0
                ])
                
                if has_change:
                    print(f"✅ 检测到流量更新")
                    return True
                else:
                    print(f"❌ 未检测到流量更新")
                    return False
            
            return current_stats
            
        except Exception as e:
            print(f"❌ 检查流量更新失败: {e}")
            return False
    
    def cleanup_user(self, user_id):
        """清理测试用户"""
        print(f"\n🗑️ 清理测试用户...")
        
        try:
            response = self.session.delete(f"{self.api_base_url}/api/user/delete?user_id={user_id}")
            response.raise_for_status()
            result = response.json()
            
            if result.get('success'):
                print(f"✅ 测试用户清理成功")
            else:
                print(f"❌ 测试用户清理失败")
        except Exception as e:
            print(f"❌ 清理测试用户异常: {e}")
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 CNS 客户端测试")
        print("=" * 40)
        
        # 1. 创建测试用户
        user_info = self.create_test_user()
        if not user_info:
            return False
        
        try:
            # 2. 获取初始流量统计
            before_stats = self.check_traffic_update(user_info['user_id'])
            if not before_stats:
                return False
            
            # 3. 测试Basic认证
            print(f"\n" + "="*40)
            basic_auth_success = self.test_proxy_with_basic_auth(user_info)
            
            # 4. 等待流量统计更新
            print(f"\n⏳ 等待5秒让流量统计更新...")
            time.sleep(5)
            
            # 5. 检查流量更新
            traffic_updated_basic = self.check_traffic_update(user_info['user_id'], before_stats)
            
            # 6. 测试User-ID头
            print(f"\n" + "="*40)
            user_id_success = self.test_proxy_with_user_id(user_info)
            
            # 7. 再次等待和检查
            print(f"\n⏳ 等待5秒让流量统计更新...")
            time.sleep(5)
            
            # 8. 最终检查
            final_traffic_updated = self.check_traffic_update(user_info['user_id'], before_stats)
            
            # 9. 总结结果
            print(f"\n📋 测试结果总结:")
            print(f"   Basic认证连接: {'✅ 成功' if basic_auth_success else '❌ 失败'}")
            print(f"   User-ID头连接: {'✅ 成功' if user_id_success else '❌ 失败'}")
            print(f"   流量统计更新: {'✅ 正常' if final_traffic_updated else '❌ 异常'}")
            
            if (basic_auth_success or user_id_success) and final_traffic_updated:
                print(f"\n🎉 测试通过: 用户认证和流量统计功能正常")
                return True
            else:
                print(f"\n❌ 测试失败: 请检查配置和代码")
                return False
        
        finally:
            # 10. 清理测试用户
            self.cleanup_user(user_info['user_id'])

def main():
    client = CNSTestClient()
    success = client.run_test()
    
    if not success:
        print(f"\n💡 故障排除建议:")
        print(f"1. 确保CNS服务正在运行")
        print(f"2. 检查配置文件中 Enable_traffic_control 是否为 true")
        print(f"3. 查看CNS服务的日志输出")
        print(f"4. 运行配置检查: python check_config.py")
        print(f"5. 运行完整诊断: python debug_connection.py")

if __name__ == "__main__":
    main()
