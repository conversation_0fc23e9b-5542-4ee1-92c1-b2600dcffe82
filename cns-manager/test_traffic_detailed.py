#!/usr/bin/env python3
"""
详细的流量统计测试
"""

import requests
import json
import time
import base64
import socket
import threading

def create_test_user():
    """创建测试用户"""
    print("👤 创建测试用户...")
    
    username = f"traffic_test_{int(time.time())}"
    password = "test123"
    quota = 100 * 1024 * 1024  # 100MB
    
    try:
        data = {
            "username": username,
            "password": password,
            "quota": quota
        }
        
        response = requests.post("http://localhost:8080/api/user/add", json=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        
        user_id = result['user_id']
        print(f"✅ 测试用户创建成功: {username} (ID: {user_id})")
        
        return {
            'user_id': user_id,
            'username': username,
            'password': password,
            'quota': quota
        }
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        return None

def get_user_stats(user_id):
    """获取用户统计信息"""
    try:
        # 获取数据库中的用户信息
        response = requests.get(f"http://localhost:8080/api/user/info/{user_id}", timeout=10)
        if response.status_code != 200:
            return None
        
        user_info = response.json()
        
        # 尝试获取实时统计
        try:
            response = requests.get(f"http://localhost:8080/api/stats/realtime/{user_id}", timeout=10)
            if response.status_code == 200:
                realtime_info = response.json()
            else:
                realtime_info = {
                    'realtime_upload': 0,
                    'realtime_download': 0,
                    'total_upload': user_info['upload'],
                    'total_download': user_info['download']
                }
        except:
            realtime_info = {
                'realtime_upload': 0,
                'realtime_download': 0,
                'total_upload': user_info['upload'],
                'total_download': user_info['download']
            }
        
        return {
            'db_upload': user_info['upload'],
            'db_download': user_info['download'],
            'realtime_upload': realtime_info['realtime_upload'],
            'realtime_download': realtime_info['realtime_download'],
            'total_upload': realtime_info['total_upload'],
            'total_download': realtime_info['total_download']
        }
    except Exception as e:
        print(f"❌ 获取用户统计失败: {e}")
        return None

def test_http_proxy_with_data(username, password, user_id):
    """测试HTTP代理并传输一些数据"""
    print(f"\n🔗 测试HTTP代理连接并传输数据...")
    
    try:
        # 创建Basic认证字符串
        auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
        
        # 连接代理
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(15)
        sock.connect(('localhost', 1254))
        
        # 发送CONNECT请求
        connect_request = f"CONNECT httpbin.org:80 HTTP/1.1\r\n"
        connect_request += f"Host: httpbin.org:80\r\n"
        connect_request += f"Authorization: Basic {auth_string}\r\n"
        connect_request += f"User-ID: {user_id}\r\n"  # 同时发送User-ID
        connect_request += f"Proxy-Connection: keep-alive\r\n"
        connect_request += f"\r\n"
        
        print(f"📤 发送CONNECT请求 (包含User-ID: {user_id})")
        sock.send(connect_request.encode())
        
        # 接收代理响应
        response = sock.recv(1024).decode()
        print(f"📥 代理响应: {response.strip()}")
        
        if "200 Connection established" in response:
            print(f"✅ 代理连接建立成功")
            
            # 发送HTTP请求获取一些数据
            http_request = "GET /json HTTP/1.1\r\n"
            http_request += "Host: httpbin.org\r\n"
            http_request += "User-Agent: CNS-Traffic-Test\r\n"
            http_request += "Accept: application/json\r\n"
            http_request += "Connection: close\r\n"
            http_request += "\r\n"
            
            print(f"📤 发送HTTP请求...")
            sock.send(http_request.encode())
            
            # 接收HTTP响应
            http_response = b""
            total_received = 0
            while True:
                try:
                    data = sock.recv(4096)
                    if not data:
                        break
                    http_response += data
                    total_received += len(data)
                    print(f"📥 接收数据: {len(data)} bytes (总计: {total_received} bytes)")
                except socket.timeout:
                    break
            
            print(f"📊 总接收数据: {total_received} bytes")
            
            # 检查响应内容
            if b"httpbin.org" in http_response or b"origin" in http_response:
                print(f"✅ HTTP请求成功，收到有效响应")
                sock.close()
                return True, total_received
            else:
                print(f"⚠️ HTTP响应可能有问题")
                sock.close()
                return True, total_received
        else:
            print(f"❌ 代理连接失败")
            sock.close()
            return False, 0
            
    except Exception as e:
        print(f"❌ 代理连接异常: {e}")
        return False, 0

def test_tcp_proxy_with_data(username, password, user_id):
    """测试TCP代理连接"""
    print(f"\n🔗 测试TCP代理连接...")
    
    try:
        # 使用requests通过代理
        proxies = {
            'http': f'http://{username}:{password}@localhost:1254',
            'https': f'http://{username}:{password}@localhost:1254'
        }
        
        headers = {
            'User-ID': user_id,
            'User-Agent': 'CNS-Traffic-Test'
        }
        
        print(f"📤 通过代理发送HTTP请求...")
        response = requests.get('http://httpbin.org/json', 
                              proxies=proxies, 
                              headers=headers,
                              timeout=15)
        
        if response.status_code == 200:
            data_size = len(response.content)
            print(f"✅ TCP代理请求成功")
            print(f"📊 响应大小: {data_size} bytes")
            return True, data_size
        else:
            print(f"❌ TCP代理请求失败: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ TCP代理连接异常: {e}")
        return False, 0

def cleanup_user(user_id):
    """清理测试用户"""
    print(f"\n🗑️ 清理测试用户...")
    
    try:
        response = requests.delete(f"http://localhost:8080/api/user/delete?user_id={user_id}", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 测试用户清理成功")
            else:
                print(f"❌ 测试用户清理失败")
        else:
            print(f"❌ 清理请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 清理测试用户异常: {e}")

def main():
    print("🚀 CNS 详细流量统计测试")
    print("=" * 50)
    
    # 1. 创建测试用户
    user_info = create_test_user()
    if not user_info:
        return False
    
    try:
        # 2. 获取初始统计
        print(f"\n📊 获取初始流量统计...")
        before_stats = get_user_stats(user_info['user_id'])
        if not before_stats:
            print(f"❌ 无法获取初始统计")
            return False
        
        print(f"📈 初始统计:")
        print(f"   数据库上传: {before_stats['db_upload']} bytes")
        print(f"   数据库下载: {before_stats['db_download']} bytes")
        print(f"   实时上传: {before_stats['realtime_upload']} bytes")
        print(f"   实时下载: {before_stats['realtime_download']} bytes")
        
        # 3. 测试HTTP代理连接
        print(f"\n" + "="*50)
        http_success, http_data = test_http_proxy_with_data(
            user_info['username'], 
            user_info['password'],
            user_info['user_id']
        )
        
        # 4. 等待流量统计更新
        print(f"\n⏳ 等待10秒让流量统计更新...")
        time.sleep(10)
        
        # 5. 检查流量更新
        print(f"\n📊 检查流量更新...")
        after_stats = get_user_stats(user_info['user_id'])
        if not after_stats:
            print(f"❌ 无法获取更新后统计")
            return False
        
        print(f"📈 更新后统计:")
        print(f"   数据库上传: {after_stats['db_upload']} bytes")
        print(f"   数据库下载: {after_stats['db_download']} bytes")
        print(f"   实时上传: {after_stats['realtime_upload']} bytes")
        print(f"   实时下载: {after_stats['realtime_download']} bytes")
        
        # 6. 计算变化
        print(f"\n📊 流量变化:")
        db_upload_change = after_stats['db_upload'] - before_stats['db_upload']
        db_download_change = after_stats['db_download'] - before_stats['db_download']
        realtime_upload_change = after_stats['realtime_upload'] - before_stats['realtime_upload']
        realtime_download_change = after_stats['realtime_download'] - before_stats['realtime_download']
        
        print(f"   数据库上传变化: +{db_upload_change} bytes")
        print(f"   数据库下载变化: +{db_download_change} bytes")
        print(f"   实时上传变化: +{realtime_upload_change} bytes")
        print(f"   实时下载变化: +{realtime_download_change} bytes")
        
        # 7. 分析结果
        has_traffic_update = (
            db_upload_change > 0 or db_download_change > 0 or
            realtime_upload_change > 0 or realtime_download_change > 0
        )
        
        print(f"\n📋 测试结果:")
        print(f"   HTTP代理连接: {'✅ 成功' if http_success else '❌ 失败'}")
        print(f"   预期数据传输: {http_data} bytes")
        print(f"   流量统计更新: {'✅ 正常' if has_traffic_update else '❌ 异常'}")
        
        if http_success and has_traffic_update:
            print(f"\n🎉 测试完全成功: 连接和流量统计都正常工作")
            return True
        elif http_success and not has_traffic_update:
            print(f"\n⚠️ 部分成功: 连接正常但流量统计未更新")
            print(f"   可能原因:")
            print(f"   1. 流量统计保存间隔较长 (30秒)")
            print(f"   2. 认证用户ID未正确传递到流量统计模块")
            print(f"   3. TrafficConn包装器未被使用")
            return False
        else:
            print(f"\n❌ 测试失败: 连接有问题")
            return False
    
    finally:
        # 8. 清理测试用户
        cleanup_user(user_info['user_id'])

if __name__ == "__main__":
    main()
