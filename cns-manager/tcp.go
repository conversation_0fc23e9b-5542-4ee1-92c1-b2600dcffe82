package main

import (
	"bytes"
	"log"
	"net"
	"strings"
	"sync"
	"time"
)

var tcpBufferPool sync.Pool = sync.Pool{
	New: func() interface{} {
		return make([]byte, 8192)
	},
}

/* 把fromConn的数据转发到toConn */
func tcpForward(fromConn, toConn net.Conn, payload []byte) {
	defer func() {
		fromConn.Close()
		toConn.Close()
	}()

	var RLen, WLen, CuteBi_XorCrypt_passwordSub int
	var err error
	for {
		fromConn.SetReadDeadline(time.Now().Add(config.Tcp_timeout))
		toConn.SetReadDeadline(time.Now().Add(config.Tcp_timeout))
		if RLen, err = fromConn.Read(payload); err != nil || RLen <= 0 {
			return
		}
		if len(CuteBi_XorCrypt_password) != 0 {
			CuteBi_XorCrypt_passwordSub = CuteBi_XorCrypt(payload[:RLen], CuteBi_XorCrypt_passwordSub)
		}
		toConn.SetWriteDeadline(time.Now().Add(config.Tcp_timeout))
		if WLen, err = toConn.Write(payload[:RLen]); err != nil || WLen <= 0 {
			return
		}
	}
}

/* 带流量统计的数据转发 */
func tcpForwardWithTraffic(fromConn, toConn net.Conn, payload []byte, userID string, isUpload bool) {
	defer func() {
		fromConn.Close()
		toConn.Close()
	}()

	var RLen, WLen, CuteBi_XorCrypt_passwordSub int
	var err error
	for {
		fromConn.SetReadDeadline(time.Now().Add(config.Tcp_timeout))
		toConn.SetReadDeadline(time.Now().Add(config.Tcp_timeout))
		if RLen, err = fromConn.Read(payload); err != nil || RLen <= 0 {
			return
		}
		if len(CuteBi_XorCrypt_password) != 0 {
			CuteBi_XorCrypt_passwordSub = CuteBi_XorCrypt(payload[:RLen], CuteBi_XorCrypt_passwordSub)
		}
		toConn.SetWriteDeadline(time.Now().Add(config.Tcp_timeout))
		if WLen, err = toConn.Write(payload[:RLen]); err != nil || WLen <= 0 {
			return
		}

		// 记录流量
		if config.Enable_traffic_control && userID != "" {
			if isUpload {
				recordTraffic(userID, int64(WLen), 0)
			} else {
				recordTraffic(userID, 0, int64(WLen))
			}
		}
	}
}

/* 从header中获取host */
func getProxyHost(header []byte) string {
	hostSub := bytes.Index(header, []byte(config.Proxy_key))
	if hostSub < 0 {
		return ""
	}
	hostSub += len(config.Proxy_key)
	hostEndSub := bytes.IndexByte(header[hostSub:], '\r')
	if hostEndSub < 0 {
		return ""
	}
	hostEndSub += hostSub
	if len(CuteBi_XorCrypt_password) != 0 {
		host, err := CuteBi_decrypt_host(header[hostSub:hostEndSub])
		if err != nil {
			log.Println(err)
			return ""
		}
		return string(host)
	} else {
		return string(header[hostSub:hostEndSub])
	}
}

/* 处理tcp会话 */
func handleTcpSession(cConn net.Conn, header []byte) {
	handleTcpSessionWithAuth(cConn, header, "")
}

/* 带用户认证的TCP会话处理 */
func handleTcpSessionWithAuth(cConn net.Conn, header []byte, userID string) {
	// defer log.Println("A tcp client close")

	/* 获取请求头中的host */
	host := getProxyHost(header)
	if host == "" {
		log.Println("No proxy host: {" + string(header) + "}")
		cConn.Write([]byte("No proxy host"))
		cConn.Close()
		return
	}

	// 如果启用流量控制且有用户ID，检查用户配额
	if config.Enable_traffic_control && userID != "" {
		if !checkUserQuota(userID) {
			log.Printf("用户 %s 流量配额不足", userID)
			cConn.Write([]byte("Traffic quota exceeded"))
			cConn.Close()
			return
		}
	}

	// log.Println("proxyHost: " + host)
	//tcpDNS over udpDNS
	if config.Enable_dns_tcpOverUdp && strings.HasSuffix(host, ":53") == true {
		dns_tcpOverUdp(cConn, host, header)
		return
	}
	/* 连接目标地址 */
	if strings.Contains(host, ":") == false {
		host += ":80"
	}
	sConn, dialErr := net.Dial("tcp", host)
	if dialErr != nil {
		log.Println(dialErr)
		cConn.Write([]byte("Proxy address [" + host + "] DialTCP() error"))
		cConn.Close()
		return
	}

	// 记录连接日志
	var logID int64
	if config.Enable_traffic_control && userID != "" {
		clientIP := cConn.RemoteAddr().String()
		if id, err := logConnection(userID, clientIP, host); err == nil {
			logID = id
		}
	}

	/* 开始转发 */
	// log.Println("Start tcpForward")

	if config.Enable_traffic_control && userID != "" {
		// 使用带流量统计的转发
		go tcpForwardWithTraffic(cConn, sConn, header, userID, true)
		newBuff := tcpBufferPool.Get().([]byte)
		tcpForwardWithTraffic(sConn, cConn, newBuff, userID, false)
		tcpBufferPool.Put(newBuff)

		// 更新连接日志
		if logID > 0 {
			upload, download := getUserTrafficStats(userID)
			updateConnectionLog(logID, upload, download)
		}
	} else {
		// 使用普通转发
		go tcpForward(cConn, sConn, header)
		newBuff := tcpBufferPool.Get().([]byte)
		tcpForward(sConn, cConn, newBuff)
		tcpBufferPool.Put(newBuff)
	}
}
