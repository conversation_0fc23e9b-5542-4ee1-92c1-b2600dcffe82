#!/bin/bash

# CNS 流量控制系统测试脚本

echo "🚀 CNS 流量控制系统测试"
echo "=========================="

# 检查服务是否运行
check_service() {
    echo "📡 检查服务状态..."
    
    # 检查API服务
    if curl -s http://localhost:8080/api/system/status > /dev/null; then
        echo "✅ API服务运行正常 (端口 8080)"
    else
        echo "❌ API服务未运行或无法访问"
        return 1
    fi
    
    # 检查Web管理界面
    if curl -s http://localhost:8081 > /dev/null; then
        echo "✅ Web管理界面运行正常 (端口 8081)"
    else
        echo "❌ Web管理界面未运行或无法访问"
    fi
    
    # 检查代理服务
    if netstat -ln | grep -q ":1254"; then
        echo "✅ 代理服务运行正常 (端口 1254)"
    else
        echo "❌ 代理服务未运行"
    fi
}

# 测试API功能
test_api() {
    echo -e "\n🧪 测试API功能..."
    
    # 生成测试用户名
    TEST_USER="testuser_$(date +%s)"
    TEST_PASS="testpass123"
    
    echo "👤 创建测试用户: $TEST_USER"
    
    # 添加用户
    USER_RESPONSE=$(curl -s -X POST http://localhost:8080/api/user/add \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASS\",\"quota\":1073741824}")
    
    if echo "$USER_RESPONSE" | grep -q "user_id"; then
        USER_ID=$(echo "$USER_RESPONSE" | grep -o '"user_id":"[^"]*"' | cut -d'"' -f4)
        echo "✅ 用户创建成功，ID: $USER_ID"
        
        # 测试用户登录
        echo "🔐 测试用户登录..."
        LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
            -H "Content-Type: application/json" \
            -d "{\"username\":\"$TEST_USER\",\"password\":\"$TEST_PASS\"}")
        
        if echo "$LOGIN_RESPONSE" | grep -q "success"; then
            echo "✅ 用户登录成功"
        else
            echo "❌ 用户登录失败"
        fi
        
        # 测试获取用户信息
        echo "📊 测试获取用户信息..."
        USER_INFO=$(curl -s http://localhost:8080/api/user/info/$USER_ID)
        if echo "$USER_INFO" | grep -q "username"; then
            echo "✅ 获取用户信息成功"
        else
            echo "❌ 获取用户信息失败"
        fi
        
        # 测试获取流量信息
        echo "📈 测试获取流量信息..."
        TRAFFIC_INFO=$(curl -s "http://localhost:8080/api/user/traffic?user_id=$USER_ID")
        if echo "$TRAFFIC_INFO" | grep -q "quota"; then
            echo "✅ 获取流量信息成功"
        else
            echo "❌ 获取流量信息失败"
        fi
        
        # 测试重置流量
        echo "🔄 测试重置流量..."
        RESET_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/user/reset?user_id=$USER_ID")
        if echo "$RESET_RESPONSE" | grep -q "success"; then
            echo "✅ 重置流量成功"
        else
            echo "❌ 重置流量失败"
        fi
        
        # 测试设置过期时间
        echo "⏰ 测试设置过期时间..."
        EXPIRE_RESPONSE=$(curl -s -X POST http://localhost:8080/api/user/expire/$USER_ID \
            -H "Content-Type: application/json" \
            -d '{"expire_at":"2024-12-31 23:59:59"}')
        if echo "$EXPIRE_RESPONSE" | grep -q "success"; then
            echo "✅ 设置过期时间成功"
        else
            echo "❌ 设置过期时间失败"
        fi
        
        # 清理测试用户
        echo "🗑️  清理测试用户..."
        DELETE_RESPONSE=$(curl -s -X DELETE "http://localhost:8080/api/user/delete?user_id=$USER_ID")
        if echo "$DELETE_RESPONSE" | grep -q "success"; then
            echo "✅ 删除测试用户成功"
        else
            echo "❌ 删除测试用户失败"
        fi
        
    else
        echo "❌ 用户创建失败: $USER_RESPONSE"
    fi
}

# 测试系统状态
test_system_status() {
    echo -e "\n🖥️  测试系统状态..."
    
    STATUS_RESPONSE=$(curl -s http://localhost:8080/api/system/status)
    if echo "$STATUS_RESPONSE" | grep -q "online_users"; then
        echo "✅ 系统状态获取成功"
        echo "📊 系统信息:"
        echo "$STATUS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATUS_RESPONSE"
    else
        echo "❌ 系统状态获取失败"
    fi
}

# 测试用户列表
test_user_list() {
    echo -e "\n👥 测试用户列表..."
    
    USERS_RESPONSE=$(curl -s "http://localhost:8080/api/system/users?page=1&limit=5")
    if echo "$USERS_RESPONSE" | grep -q "users"; then
        echo "✅ 用户列表获取成功"
        USER_COUNT=$(echo "$USERS_RESPONSE" | grep -o '"total":[0-9]*' | cut -d':' -f2)
        echo "📊 总用户数: $USER_COUNT"
    else
        echo "❌ 用户列表获取失败"
    fi
}

# 测试数据库
test_database() {
    echo -e "\n🗄️  测试数据库..."
    
    if [ -f "cns_users.db" ]; then
        echo "✅ 数据库文件存在"
        
        # 检查表结构
        if command -v sqlite3 > /dev/null; then
            echo "📋 数据库表:"
            sqlite3 cns_users.db ".tables"
            
            echo "📊 用户表记录数:"
            USER_COUNT=$(sqlite3 cns_users.db "SELECT COUNT(*) FROM users;")
            echo "   用户数: $USER_COUNT"
            
            CONN_COUNT=$(sqlite3 cns_users.db "SELECT COUNT(*) FROM connection_logs;")
            echo "   连接记录数: $CONN_COUNT"
            
            STATS_COUNT=$(sqlite3 cns_users.db "SELECT COUNT(*) FROM traffic_stats;")
            echo "   统计记录数: $STATS_COUNT"
        else
            echo "⚠️  sqlite3 命令未找到，跳过数据库详细检查"
        fi
    else
        echo "❌ 数据库文件不存在"
    fi
}

# 性能测试
performance_test() {
    echo -e "\n⚡ 简单性能测试..."
    
    echo "🔄 并发API请求测试..."
    
    # 创建临时测试脚本
    cat > /tmp/api_test.sh << 'EOF'
#!/bin/bash
for i in {1..10}; do
    curl -s http://localhost:8080/api/system/status > /dev/null &
done
wait
EOF
    
    chmod +x /tmp/api_test.sh
    
    # 测试API响应时间
    start_time=$(date +%s.%N)
    /tmp/api_test.sh
    end_time=$(date +%s.%N)
    
    duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "计算失败")
    echo "✅ 10个并发请求完成时间: ${duration}秒"
    
    # 清理
    rm -f /tmp/api_test.sh
}

# 生成测试报告
generate_report() {
    echo -e "\n📋 测试报告"
    echo "============"
    echo "测试时间: $(date)"
    echo "测试项目:"
    echo "  ✓ 服务状态检查"
    echo "  ✓ API功能测试"
    echo "  ✓ 系统状态测试"
    echo "  ✓ 用户列表测试"
    echo "  ✓ 数据库测试"
    echo "  ✓ 性能测试"
    echo ""
    echo "🌐 访问地址:"
    echo "  API服务: http://localhost:8080"
    echo "  Web管理: http://localhost:8081"
    echo "  代理服务: localhost:1254"
    echo ""
    echo "📚 更多信息请查看 TRAFFIC_CONTROL_README.md"
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl > /dev/null; then
        echo "❌ curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    # 运行测试
    check_service
    if [ $? -eq 0 ]; then
        test_api
        test_system_status
        test_user_list
        test_database
        performance_test
        generate_report
    else
        echo "❌ 服务未运行，请先启动 CNS 服务"
        echo "启动命令: ./cns -json=config.json"
        exit 1
    fi
}

# 运行主函数
main "$@"
