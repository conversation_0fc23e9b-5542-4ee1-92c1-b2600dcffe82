#!/usr/bin/env python3
"""
CNS 配置检查脚本
检查配置文件和系统设置是否正确
"""

import json
import os
import sqlite3
import sys

def check_config_file():
    """检查配置文件"""
    print("🔍 检查配置文件...")
    
    config_files = ['config.json', 'config_arm.json']
    config = None
    config_file = None
    
    for file in config_files:
        if os.path.exists(file):
            config_file = file
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ 找到配置文件: {file}")
                break
            except Exception as e:
                print(f"❌ 配置文件 {file} 格式错误: {e}")
    
    if not config:
        print("❌ 未找到有效的配置文件")
        return None
    
    # 检查关键配置项
    print(f"\n📋 配置检查:")
    
    # 检查流量控制是否启用
    traffic_control = config.get('Enable_traffic_control', False)
    print(f"   流量控制: {'✅ 启用' if traffic_control else '❌ 禁用'}")
    
    if not traffic_control:
        print(f"   ⚠️ 流量控制未启用，用户信息不会更新")
        print(f"   💡 请在配置文件中设置 'Enable_traffic_control': true")
    
    # 检查监听地址
    listen_addr = config.get('Listen_addr', [])
    print(f"   监听地址: {listen_addr}")
    
    # 检查API端口
    api_port = config.get('API_port', ':8080')
    print(f"   API端口: {api_port}")
    
    # 检查代理密钥
    proxy_key = config.get('proxy_key', 'Host')
    print(f"   代理密钥: {proxy_key}")
    
    # 检查加密密码
    encrypt_password = config.get('encrypt_password', '')
    print(f"   加密密码: {'已设置' if encrypt_password else '未设置'}")
    
    # 检查超时设置
    tcp_timeout = config.get('Tcp_timeout', 300)
    udp_timeout = config.get('Udp_timeout', 30)
    print(f"   TCP超时: {tcp_timeout}秒")
    print(f"   UDP超时: {udp_timeout}秒")
    
    return config

def check_database():
    """检查数据库"""
    print(f"\n🗄️ 检查数据库...")
    
    db_files = ['cns_users.db', 'cns_users.json']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"✅ 找到数据库文件: {db_file}")
            
            if db_file.endswith('.db'):
                # SQLite数据库
                try:
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    
                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    table_names = [table[0] for table in tables]
                    
                    print(f"   数据库表: {table_names}")
                    
                    # 检查用户表
                    if 'users' in table_names:
                        cursor.execute("SELECT COUNT(*) FROM users")
                        user_count = cursor.fetchone()[0]
                        print(f"   用户数量: {user_count}")
                        
                        if user_count > 0:
                            cursor.execute("SELECT user_id, username, quota, upload, download, status FROM users LIMIT 5")
                            users = cursor.fetchall()
                            print(f"   示例用户:")
                            for user in users:
                                user_id, username, quota, upload, download, status = user
                                quota_mb = quota / 1024 / 1024
                                upload_mb = upload / 1024 / 1024
                                download_mb = download / 1024 / 1024
                                status_text = "正常" if status == 1 else "禁用"
                                print(f"     {username}: {upload_mb:.2f}/{download_mb:.2f}/{quota_mb:.2f}MB - {status_text}")
                    else:
                        print(f"   ❌ 用户表不存在")
                    
                    conn.close()
                    
                except Exception as e:
                    print(f"   ❌ 数据库访问错误: {e}")
            
            elif db_file.endswith('.json'):
                # JSON数据库
                try:
                    with open(db_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    users = data.get('users', {})
                    print(f"   用户数量: {len(users)}")
                    
                    if users:
                        print(f"   示例用户:")
                        for user_id, user_info in list(users.items())[:5]:
                            username = user_info.get('username', 'Unknown')
                            quota = user_info.get('quota', 0)
                            upload = user_info.get('upload', 0)
                            download = user_info.get('download', 0)
                            status = user_info.get('status', 0)
                            
                            quota_mb = quota / 1024 / 1024
                            upload_mb = upload / 1024 / 1024
                            download_mb = download / 1024 / 1024
                            status_text = "正常" if status == 1 else "禁用"
                            print(f"     {username}: {upload_mb:.2f}/{download_mb:.2f}/{quota_mb:.2f}MB - {status_text}")
                
                except Exception as e:
                    print(f"   ❌ JSON数据库访问错误: {e}")
        else:
            print(f"   ⚠️ 数据库文件不存在: {db_file}")

def check_executable():
    """检查可执行文件"""
    print(f"\n🔧 检查可执行文件...")
    
    executables = [
        'cns.exe',
        'cns',
        'cns-amd64-linux',
        'cns-arm64-linux',
        'cns-arm32-linux',
        'cns-arm64-linux-pure',
        'cns-arm32-linux-pure'
    ]
    
    found_executables = []
    for exe in executables:
        if os.path.exists(exe):
            size = os.path.getsize(exe)
            size_mb = size / 1024 / 1024
            print(f"✅ 找到可执行文件: {exe} ({size_mb:.2f} MB)")
            found_executables.append(exe)
    
    if not found_executables:
        print(f"❌ 未找到可执行文件")
        print(f"💡 请先编译程序: go build -o cns")
    
    return found_executables

def check_ports():
    """检查端口占用"""
    print(f"\n🌐 检查端口占用...")
    
    import socket
    
    ports_to_check = [1254, 8080, 8081, 8978]
    
    for port in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ 端口 {port} 正在使用")
            else:
                print(f"⚠️ 端口 {port} 未使用")
        except Exception as e:
            print(f"❌ 检查端口 {port} 失败: {e}")

def analyze_authentication_issue():
    """分析认证问题"""
    print(f"\n🔍 分析可能的认证问题...")
    
    issues = []
    solutions = []
    
    # 检查配置文件
    if not os.path.exists('config.json') and not os.path.exists('config_arm.json'):
        issues.append("配置文件不存在")
        solutions.append("创建配置文件并设置 Enable_traffic_control: true")
    
    # 检查数据库
    if not os.path.exists('cns_users.db') and not os.path.exists('cns_users.json'):
        issues.append("数据库文件不存在")
        solutions.append("启动程序后会自动创建数据库，或手动创建用户")
    
    print(f"🔧 可能的问题和解决方案:")
    
    print(f"\n1. 用户认证问题:")
    print(f"   问题: 客户端未正确发送认证信息")
    print(f"   解决: 确保客户端在HTTP头中包含以下信息:")
    print(f"         Authorization: Basic <base64编码的用户名:密码>")
    print(f"         User-ID: <用户ID>")
    
    print(f"\n2. 流量控制未启用:")
    print(f"   问题: 配置文件中 Enable_traffic_control 为 false")
    print(f"   解决: 在配置文件中设置 'Enable_traffic_control': true")
    
    print(f"\n3. 数据库权限问题:")
    print(f"   问题: 程序无法写入数据库")
    print(f"   解决: 检查数据库文件权限，确保程序有读写权限")
    
    print(f"\n4. 流量统计延迟:")
    print(f"   问题: 流量统计每30秒保存一次")
    print(f"   解决: 等待30秒后再检查，或查看实时统计")
    
    print(f"\n5. 认证逻辑问题:")
    print(f"   问题: authenticateFromHeader 函数实现不完整")
    print(f"   解决: 检查代码中的用户认证逻辑")

def main():
    print("🚀 CNS 配置和问题诊断")
    print("=" * 40)
    
    # 检查配置文件
    config = check_config_file()
    
    # 检查数据库
    check_database()
    
    # 检查可执行文件
    executables = check_executable()
    
    # 检查端口
    check_ports()
    
    # 分析认证问题
    analyze_authentication_issue()
    
    print(f"\n📋 诊断总结:")
    
    if config and config.get('Enable_traffic_control'):
        print(f"✅ 流量控制已启用")
    else:
        print(f"❌ 流量控制未启用 - 这是用户信息不更新的主要原因")
    
    if os.path.exists('cns_users.db') or os.path.exists('cns_users.json'):
        print(f"✅ 数据库文件存在")
    else:
        print(f"❌ 数据库文件不存在")
    
    if executables:
        print(f"✅ 找到可执行文件")
    else:
        print(f"❌ 未找到可执行文件")
    
    print(f"\n💡 下一步建议:")
    print(f"1. 运行诊断脚本: python debug_connection.py")
    print(f"2. 检查程序日志输出")
    print(f"3. 使用API测试用户创建和认证")
    print(f"4. 验证客户端认证信息格式")

if __name__ == "__main__":
    main()
