#!/bin/bash

# CNS ARM 成功编译脚本

echo "🔧 CNS ARM 成功编译脚本"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查Go环境
check_go() {
    echo -e "${YELLOW}📦 检查Go环境...${NC}"
    
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装${NC}"
        return 1
    fi
    
    GO_VERSION=$(go version)
    echo -e "${GREEN}✅ Go环境: $GO_VERSION${NC}"
    return 0
}

# 设置Go代理
setup_proxy() {
    echo -e "${YELLOW}🌐 设置Go代理...${NC}"
    
    export GOPROXY=https://goproxy.cn,direct
    export GOSUMDB=sum.golang.google.cn
    export GO111MODULE=on
    
    echo -e "${BLUE}GOPROXY: $GOPROXY${NC}"
}

# 在独立目录中编译ARM版本
build_arm_versions() {
    echo -e "${YELLOW}📁 在独立目录中编译ARM版本...${NC}"
    
    # 创建临时编译目录
    BUILD_DIR="arm_build_final"
    rm -rf $BUILD_DIR
    mkdir -p $BUILD_DIR
    
    # 复制必要的源文件
    cp cns.go $BUILD_DIR/
    cp api.go $BUILD_DIR/
    cp web_admin.go $BUILD_DIR/
    cp tcp.go $BUILD_DIR/
    cp udp.go $BUILD_DIR/
    cp http_tunnel.go $BUILD_DIR/
    cp dns.go $BUILD_DIR/
    cp tlsSide.go $BUILD_DIR/
    cp CuteBi_XorCrypt.go $BUILD_DIR/
    cp sys_isWin.go $BUILD_DIR/ 2>/dev/null || true
    cp sys_isNotWin.go $BUILD_DIR/ 2>/dev/null || true
    
    # 进入编译目录
    cd $BUILD_DIR
    
    # 创建纯Go版本的go.mod
    cat > go.mod << 'EOF'
module cns

go 1.18

require (
    github.com/google/uuid v1.3.0
    github.com/gorilla/mux v1.8.0
)
EOF

    cat > go.sum << 'EOF'
github.com/google/uuid v1.3.0 h1:t6JiXgmwXMjEs8VusXIJk2BXHsn+wx8BZdTaoZ5fu7I=
github.com/google/uuid v1.3.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
github.com/gorilla/mux v1.8.0 h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=
github.com/gorilla/mux v1.8.0/go.mod h1:DVbg23sWSpFRCP0SfiEN6jmj59UnW/n46BH5rLB71So=
EOF

    # 创建纯Go数据库文件
    cat > db.go << 'EOF'
package main

import (
    "encoding/json"
    "log"
    "os"
    "sync"
    "time"
)

type MemoryDB struct {
    Users           map[string]*User           `json:"users"`
    ConnectionLogs  []ConnectionLog            `json:"connection_logs"`
    TrafficStats    []TrafficStat              `json:"traffic_stats"`
    mutex           sync.RWMutex
}

type User struct {
    UserID    string     `json:"user_id"`
    Username  string     `json:"username"`
    Password  string     `json:"password"`
    Quota     int64      `json:"quota"`
    Upload    int64      `json:"upload"`
    Download  int64      `json:"download"`
    CreatedAt time.Time  `json:"created_at"`
    ExpireAt  *time.Time `json:"expire_at,omitempty"`
    Status    int        `json:"status"`
    WechatID  string     `json:"wechat_id"`
}

type ConnectionLog struct {
    ID             int64     `json:"id"`
    UserID         string    `json:"user_id"`
    ClientIP       string    `json:"client_ip"`
    TargetHost     string    `json:"target_host"`
    ConnectTime    time.Time `json:"connect_time"`
    DisconnectTime *time.Time `json:"disconnect_time,omitempty"`
    Upload         int64     `json:"upload"`
    Download       int64     `json:"download"`
}

type TrafficStat struct {
    ID          int64  `json:"id"`
    UserID      string `json:"user_id"`
    Date        string `json:"date"`
    Upload      int64  `json:"upload"`
    Download    int64  `json:"download"`
    Connections int    `json:"connections"`
}

var (
    memDB      *MemoryDB
    dbFile     = "cns_users.json"
    nextLogID  = int64(1)
    nextStatID = int64(1)
)

func initDatabase() {
    memDB = &MemoryDB{
        Users:          make(map[string]*User),
        ConnectionLogs: make([]ConnectionLog, 0),
        TrafficStats:   make([]TrafficStat, 0),
    }
    
    if data, err := os.ReadFile(dbFile); err == nil {
        if err := json.Unmarshal(data, memDB); err != nil {
            log.Printf("加载数据库文件失败: %v", err)
        } else {
            log.Println("数据库文件加载成功")
        }
    }
    
    for _, log := range memDB.ConnectionLogs {
        if log.ID >= nextLogID {
            nextLogID = log.ID + 1
        }
    }
    for _, stat := range memDB.TrafficStats {
        if stat.ID >= nextStatID {
            nextStatID = stat.ID + 1
        }
    }
    
    log.Println("数据库初始化完成 (纯Go版本)")
    
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        for range ticker.C {
            saveDatabase()
        }
    }()
}

func saveDatabase() {
    memDB.mutex.RLock()
    data, err := json.MarshalIndent(memDB, "", "  ")
    memDB.mutex.RUnlock()
    
    if err != nil {
        log.Printf("序列化数据库失败: %v", err)
        return
    }
    
    if err := os.WriteFile(dbFile, data, 0644); err != nil {
        log.Printf("保存数据库文件失败: %v", err)
    }
}

func addUser(userID, username, password string, quota int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user := &User{
        UserID:    userID,
        Username:  username,
        Password:  password,
        Quota:     quota,
        Upload:    0,
        Download:  0,
        CreatedAt: time.Now(),
        Status:    1,
    }
    
    memDB.Users[userID] = user
    log.Printf("添加用户: %s", username)
    return nil
}

func getUserTraffic(userID string) (upload, download, quota int64, err error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return 0, 0, 1073741824, nil
    }
    
    return user.Upload, user.Download, user.Quota, nil
}

func resetUserTraffic(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.Upload = 0
    user.Download = 0
    log.Printf("重置用户流量: %s", userID)
    return nil
}

func deleteUser(userID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    delete(memDB.Users, userID)
    log.Printf("删除用户: %s", userID)
    return nil
}

func bindWechatID(userID, wechatID string) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.WechatID = wechatID
    log.Printf("绑定微信ID: %s -> %s", userID, wechatID)
    return nil
}

func getUserIDByWechatID(wechatID string) (string, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    for _, user := range memDB.Users {
        if user.WechatID == wechatID {
            return user.UserID, nil
        }
    }
    
    return "", nil
}

func logConnection(userID, clientIP, targetHost string) (int64, error) {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    logID := nextLogID
    nextLogID++
    
    log := ConnectionLog{
        ID:          logID,
        UserID:      userID,
        ClientIP:    clientIP,
        TargetHost:  targetHost,
        ConnectTime: time.Now(),
        Upload:      0,
        Download:    0,
    }
    
    memDB.ConnectionLogs = append(memDB.ConnectionLogs, log)
    return logID, nil
}

func updateConnectionLog(logID int64, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    for i := range memDB.ConnectionLogs {
        if memDB.ConnectionLogs[i].ID == logID {
            now := time.Now()
            memDB.ConnectionLogs[i].DisconnectTime = &now
            memDB.ConnectionLogs[i].Upload = upload
            memDB.ConnectionLogs[i].Download = download
            break
        }
    }
    
    return nil
}

func getUserConnectionHistory(userID string, limit int) ([]map[string]interface{}, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    var history []map[string]interface{}
    count := 0
    
    for i := len(memDB.ConnectionLogs) - 1; i >= 0 && count < limit; i-- {
        log := memDB.ConnectionLogs[i]
        if log.UserID == userID {
            record := map[string]interface{}{
                "client_ip":       log.ClientIP,
                "target_host":     log.TargetHost,
                "connect_time":    log.ConnectTime.Format("2006-01-02 15:04:05"),
                "disconnect_time": "",
                "upload":          log.Upload,
                "download":        log.Download,
            }
            
            if log.DisconnectTime != nil {
                record["disconnect_time"] = log.DisconnectTime.Format("2006-01-02 15:04:05")
            }
            
            history = append(history, record)
            count++
        }
    }
    
    return history, nil
}

func updateDailyTrafficStats(userID string, upload, download int64) error {
    return nil
}

func getUserDailyStats(userID string, days int) ([]map[string]interface{}, error) {
    return []map[string]interface{}{}, nil
}

func updateUserInfo(userID string, quota *int64, status *int) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    if quota != nil {
        user.Quota = *quota
    }
    
    if status != nil {
        user.Status = *status
    }
    
    return nil
}

func setUserExpire(userID string, expireTime time.Time) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.ExpireAt = &expireTime
    return nil
}

func getTotalUserCount() (int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    return len(memDB.Users), nil
}

func getUserList(page, limit int) ([]map[string]interface{}, int, error) {
    memDB.mutex.RLock()
    defer memDB.mutex.RUnlock()
    
    total := len(memDB.Users)
    var users []map[string]interface{}
    
    for _, user := range memDB.Users {
        userMap := map[string]interface{}{
            "user_id":      user.UserID,
            "username":     user.Username,
            "quota":        user.Quota,
            "upload":       user.Upload,
            "download":     user.Download,
            "created_at":   user.CreatedAt.Format("2006-01-02 15:04:05"),
            "expire_at":    "",
            "status":       user.Status,
            "wechat_id":    user.WechatID,
            "used_percent": float64(user.Upload+user.Download) / float64(user.Quota) * 100,
        }
        
        if user.ExpireAt != nil {
            userMap["expire_at"] = user.ExpireAt.Format("2006-01-02 15:04:05")
        }
        
        users = append(users, userMap)
        if len(users) >= limit {
            break
        }
    }
    
    return users, total, nil
}

func updateUserTrafficInDB(userID string, upload, download int64) error {
    memDB.mutex.Lock()
    defer memDB.mutex.Unlock()
    
    user, exists := memDB.Users[userID]
    if !exists {
        return nil
    }
    
    user.Upload += upload
    user.Download += download
    return nil
}
EOF

    # 创建修改后的traffic_control.go（移除重复函数）
    cat > traffic_control.go << 'EOF'
package main

import (
    "log"
    "sync"
    "time"
)

// 流量统计结构
type UserTrafficStats struct {
    Upload   int64
    Download int64
    mutex    sync.Mutex
}

// 全局变量
var (
    userTrafficStats = make(map[string]*UserTrafficStats)
    trafficMutex     sync.RWMutex
    startTime        = time.Now()
)

// 初始化流量控制
func initTrafficControl() {
    log.Println("流量控制模块初始化完成")
    
    // 启动定期保存流量数据的协程
    go func() {
        ticker := time.NewTicker(10 * time.Second)
        defer ticker.Stop()
        
        for range ticker.C {
            saveTrafficStats()
        }
    }()
}

// 记录用户流量
func recordUserTraffic(userID string, upload, download int64) {
    if userID == "" {
        return
    }
    
    trafficMutex.Lock()
    if _, exists := userTrafficStats[userID]; !exists {
        userTrafficStats[userID] = &UserTrafficStats{}
    }
    
    stats := userTrafficStats[userID]
    trafficMutex.Unlock()
    
    stats.mutex.Lock()
    stats.Upload += upload
    stats.Download += download
    stats.mutex.Unlock()
}

// 获取用户实时流量
func getUserRealtimeTraffic(userID string) (upload, download int64) {
    trafficMutex.RLock()
    stats, exists := userTrafficStats[userID]
    trafficMutex.RUnlock()
    
    if !exists {
        return 0, 0
    }
    
    stats.mutex.Lock()
    upload = stats.Upload
    download = stats.Download
    stats.mutex.Unlock()
    
    return upload, download
}

// 检查用户流量配额
func checkUserQuota(userID string) bool {
    if userID == "" {
        return true // 如果没有用户ID，允许通过
    }
    
    // 获取数据库中的流量信息
    dbUpload, dbDownload, quota, err := getUserTraffic(userID)
    if err != nil {
        log.Printf("获取用户流量失败: %v", err)
        return false
    }
    
    // 获取实时流量
    realtimeUpload, realtimeDownload := getUserRealtimeTraffic(userID)
    
    // 计算总流量
    totalUpload := dbUpload + realtimeUpload
    totalDownload := dbDownload + realtimeDownload
    totalTraffic := totalUpload + totalDownload
    
    // 检查是否超过配额
    if totalTraffic >= quota {
        log.Printf("用户 %s 流量超额: %d/%d", userID, totalTraffic, quota)
        return false
    }
    
    return true
}

// 保存流量统计到数据库
func saveTrafficStats() {
    trafficMutex.RLock()
    
    // 复制当前统计数据
    statsToSave := make(map[string]UserTrafficStats)
    for userID, stats := range userTrafficStats {
        stats.mutex.Lock()
        if stats.Upload > 0 || stats.Download > 0 {
            statsToSave[userID] = UserTrafficStats{
                Upload:   stats.Upload,
                Download: stats.Download,
            }
            // 重置统计
            stats.Upload = 0
            stats.Download = 0
        }
        stats.mutex.Unlock()
    }
    trafficMutex.RUnlock()
    
    // 保存到数据库
    for userID, stats := range statsToSave {
        if stats.Upload > 0 || stats.Download > 0 {
            updateUserTrafficInDB(userID, stats.Upload, stats.Download)
        }
    }
}

// 获取用户流量统计
func getUserTrafficStats(userID string) map[string]interface{} {
    // 获取数据库中的流量
    dbUpload, dbDownload, quota, err := getUserTraffic(userID)
    if err != nil {
        log.Printf("获取用户流量失败: %v", err)
        return map[string]interface{}{
            "error": "获取流量信息失败",
        }
    }
    
    // 获取实时流量
    realtimeUpload, realtimeDownload := getUserRealtimeTraffic(userID)
    
    // 计算总流量
    totalUpload := dbUpload + realtimeUpload
    totalDownload := dbDownload + realtimeDownload
    totalTraffic := totalUpload + totalDownload
    
    // 计算使用百分比
    var usedPercent float64
    if quota > 0 {
        usedPercent = float64(totalTraffic) / float64(quota) * 100
    }
    
    return map[string]interface{}{
        "realtime": map[string]interface{}{
            "realtime_upload":   realtimeUpload,
            "realtime_download": realtimeDownload,
            "total_upload":      totalUpload,
            "total_download":    totalDownload,
            "quota":             quota,
            "used_percent":      usedPercent,
        },
    }
}

// 重置用户实时流量统计
func resetUserRealtimeTraffic(userID string) {
    trafficMutex.Lock()
    if stats, exists := userTrafficStats[userID]; exists {
        stats.mutex.Lock()
        stats.Upload = 0
        stats.Download = 0
        stats.mutex.Unlock()
    }
    trafficMutex.Unlock()
}

// 获取系统统计信息
func getSystemStats() map[string]interface{} {
    trafficMutex.RLock()
    onlineUsers := len(userTrafficStats)
    trafficMutex.RUnlock()
    
    totalUsers, _ := getTotalUserCount()
    
    return map[string]interface{}{
        "online_users": onlineUsers,
        "total_users":  totalUsers,
        "server_time":  time.Now().Format("2006-01-02 15:04:05"),
        "uptime":       time.Since(startTime).String(),
    }
}
EOF

    echo -e "${GREEN}✅ 修改后的文件创建完成${NC}"
    
    # 下载依赖
    go mod tidy
    go mod download
    
    # 编译ARM64版本
    echo -e "${YELLOW}🔨 编译ARM64版本...${NC}"
    export GOOS=linux
    export GOARCH=arm64
    export CGO_ENABLED=0
    
    go build -ldflags="-s -w" -o cns-arm64-linux-pure
    
    if [ -f "cns-arm64-linux-pure" ] && [ -s "cns-arm64-linux-pure" ]; then
        echo -e "${GREEN}✅ ARM64编译成功${NC}"
        cp cns-arm64-linux-pure ../
        ARM64_SUCCESS=true
    else
        echo -e "${RED}❌ ARM64编译失败${NC}"
        ARM64_SUCCESS=false
    fi
    
    # 编译ARM32版本
    echo -e "${YELLOW}🔨 编译ARM32版本...${NC}"
    export GOARCH=arm
    
    go build -ldflags="-s -w" -o cns-arm32-linux-pure
    
    if [ -f "cns-arm32-linux-pure" ] && [ -s "cns-arm32-linux-pure" ]; then
        echo -e "${GREEN}✅ ARM32编译成功${NC}"
        cp cns-arm32-linux-pure ../
        ARM32_SUCCESS=true
    else
        echo -e "${RED}❌ ARM32编译失败${NC}"
        ARM32_SUCCESS=false
    fi
    
    # 返回上级目录
    cd ..
    
    # 清理临时目录
    rm -rf $BUILD_DIR
    
    return 0
}

# 创建启动脚本
create_startup_script() {
    echo -e "${YELLOW}📝 创建启动脚本...${NC}"
    
    cat > start_arm_pure.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (纯Go版本)"
echo "=================================="

# 自动检测架构
ARCH=$(uname -m)
case $ARCH in
    "aarch64"|"arm64")
        if [ -f "cns-arm64-linux-pure" ]; then
            CNS_BINARY="./cns-arm64-linux-pure"
            echo "✅ 使用 ARM64 纯Go版本"
        else
            echo "❌ 未找到ARM64纯Go版本: cns-arm64-linux-pure"
            exit 1
        fi
        ;;
    "armv7l"|"arm")
        if [ -f "cns-arm32-linux-pure" ]; then
            CNS_BINARY="./cns-arm32-linux-pure"
            echo "✅ 使用 ARM32 纯Go版本"
        else
            echo "❌ 未找到ARM32纯Go版本: cns-arm32-linux-pure"
            exit 1
        fi
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        echo "支持的架构: aarch64, arm64, armv7l, arm"
        exit 1
        ;;
esac

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件 (config_arm.json 或 config.json)"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   数据存储: cns_users.json (纯Go版本)"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 特点:"
echo "   - 无CGO依赖，适合ARM设备"
echo "   - 数据存储在JSON文件中"
echo "   - 自动定期保存数据"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
EOF
    
    chmod +x start_arm_pure.sh
    echo -e "${GREEN}✅ 启动脚本创建完成: start_arm_pure.sh${NC}"
}

# 显示结果
show_results() {
    echo -e "${GREEN}📋 编译结果${NC}"
    echo "=============="
    
    success_count=0
    
    if [ -f "cns-arm64-linux-pure" ] && [ -s "cns-arm64-linux-pure" ]; then
        size=$(du -h cns-arm64-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM64 纯Go版本: cns-arm64-linux-pure ($size)${NC}"
        
        # 检查文件类型
        if command -v file &> /dev/null; then
            file_info=$(file cns-arm64-linux-pure)
            echo -e "${BLUE}   文件信息: $file_info${NC}"
        fi
        
        ((success_count++))
    fi
    
    if [ -f "cns-arm32-linux-pure" ] && [ -s "cns-arm32-linux-pure" ]; then
        size=$(du -h cns-arm32-linux-pure | cut -f1)
        echo -e "${GREEN}✅ ARM32 纯Go版本: cns-arm32-linux-pure ($size)${NC}"
        ((success_count++))
    fi
    
    echo ""
    echo -e "${BLUE}🎯 成功编译: $success_count 个ARM版本${NC}"
    
    if [ $success_count -gt 0 ]; then
        echo ""
        echo -e "${BLUE}🚀 使用方法:${NC}"
        echo "  ./start_arm_pure.sh    # 自动选择架构启动"
        echo ""
        echo -e "${BLUE}传输到ARM设备:${NC}"
        echo "  scp cns-arm64-linux-pure config_arm.json start_arm_pure.sh root@arm-device:/path/"
        echo ""
        echo -e "${BLUE}在ARM设备上运行:${NC}"
        echo "  chmod +x start_arm_pure.sh"
        echo "  ./start_arm_pure.sh"
        echo ""
        echo -e "${YELLOW}💡 特点:${NC}"
        echo "  - 无CGO依赖，解决ARM设备SQLite问题"
        echo "  - 数据存储在 cns_users.json 文件中"
        echo "  - 支持所有原有功能"
        echo "  - 自动定期保存数据"
        echo "  - 已解决函数重复声明问题"
    else
        echo -e "${RED}❌ 编译失败，请检查错误信息${NC}"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}开始ARM成功编译解决方案...${NC}"
    echo ""
    
    # 检查Go环境
    if ! check_go; then
        exit 1
    fi
    
    # 设置代理
    setup_proxy
    
    # 编译ARM版本
    build_arm_versions
    
    # 创建启动脚本
    create_startup_script
    
    # 显示结果
    show_results
    
    echo ""
    echo -e "${GREEN}🎉 ARM编译成功解决方案完成！${NC}"
}

# 运行主函数
main "$@"
