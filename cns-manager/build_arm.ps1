# CNS 流量控制系统 ARM 编译脚本 (PowerShell)

Write-Host "🚀 CNS ARM 编译脚本 (Windows)" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# 检查Go环境
function Test-GoEnvironment {
    Write-Host "`n📦 检查Go环境..." -ForegroundColor Yellow
    
    try {
        $goVersion = go version
        Write-Host "✅ Go环境检查通过: $goVersion" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Go未安装或未在PATH中" -ForegroundColor Red
        Write-Host "请先安装Go: https://golang.org/dl/" -ForegroundColor Yellow
        return $false
    }
}

# 检查系统架构
function Get-SystemInfo {
    Write-Host "`n🔍 检查系统信息..." -ForegroundColor Yellow
    
    $currentOS = go env GOOS
    $currentArch = go env GOARCH
    
    Write-Host "当前系统: $currentOS/$currentArch" -ForegroundColor Cyan
    
    return @{
        OS = $currentOS
        Arch = $currentArch
    }
}

# 安装依赖
function Install-Dependencies {
    Write-Host "`n📥 安装Go依赖..." -ForegroundColor Yellow
    
    # 初始化go.mod（如果不存在）
    if (-not (Test-Path "go.mod")) {
        go mod init cns
    }
    
    try {
        go mod tidy
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ 依赖安装失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 编译函数
function Build-ForTarget {
    param(
        [string]$TargetOS,
        [string]$TargetArch,
        [string]$OutputName
    )
    
    Write-Host "`n🔨 编译 $TargetOS/$TargetArch..." -ForegroundColor Yellow
    
    # 设置环境变量
    $env:GOOS = $TargetOS
    $env:GOARCH = $TargetArch
    $env:CGO_ENABLED = "1"
    
    # 对于ARM架构的交叉编译，通常需要禁用CGO
    if ($TargetArch -eq "arm" -or $TargetArch -eq "arm64") {
        if ($TargetOS -ne (go env GOHOSTOS) -or $TargetArch -ne (go env GOHOSTARCH)) {
            Write-Host "💡 ARM交叉编译，禁用CGO" -ForegroundColor Cyan
            $env:CGO_ENABLED = "0"
        }
    }
    
    try {
        # 编译
        $buildArgs = @(
            "build",
            "-ldflags=-s -w",
            "-o", $OutputName
        )
        
        & go @buildArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 编译成功: $OutputName" -ForegroundColor Green
            
            # 显示文件信息
            if (Test-Path $OutputName) {
                $fileInfo = Get-Item $OutputName
                $sizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
                Write-Host "📊 文件大小: $sizeKB KB" -ForegroundColor Cyan
            }
            return $true
        }
        else {
            Write-Host "❌ 编译失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ 编译异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    finally {
        # 恢复环境变量
        Remove-Item Env:GOOS -ErrorAction SilentlyContinue
        Remove-Item Env:GOARCH -ErrorAction SilentlyContinue
        Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue
    }
}

# 主编译菜单
function Show-BuildMenu {
    Write-Host "`n🎯 选择编译目标..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1) ARM 32位 Linux (arm/linux)"
    Write-Host "2) ARM 64位 Linux (arm64/linux)"
    Write-Host "3) ARM 32位 Windows (arm/windows)"
    Write-Host "4) ARM 64位 Windows (arm64/windows)"
    Write-Host "5) 当前系统架构"
    Write-Host "6) 编译所有ARM架构"
    Write-Host "7) 自定义架构"
    Write-Host ""
    
    $choice = Read-Host "请输入选择 (1-7)"
    
    switch ($choice) {
        "1" {
            Build-ForTarget "linux" "arm" "cns-arm32-linux"
        }
        "2" {
            Build-ForTarget "linux" "arm64" "cns-arm64-linux"
        }
        "3" {
            Build-ForTarget "windows" "arm" "cns-arm32-windows.exe"
        }
        "4" {
            Build-ForTarget "windows" "arm64" "cns-arm64-windows.exe"
        }
        "5" {
            $currentOS = go env GOOS
            $currentArch = go env GOARCH
            $outputName = "cns-native"
            if ($currentOS -eq "windows") {
                $outputName += ".exe"
            }
            Build-ForTarget $currentOS $currentArch $outputName
        }
        "6" {
            Write-Host "📦 编译所有ARM架构..." -ForegroundColor Cyan
            Build-ForTarget "linux" "arm" "cns-arm32-linux"
            Build-ForTarget "linux" "arm64" "cns-arm64-linux"
            Build-ForTarget "windows" "arm" "cns-arm32-windows.exe"
            Build-ForTarget "windows" "arm64" "cns-arm64-windows.exe"
        }
        "7" {
            $targetOS = Read-Host "请输入目标OS (如: linux, windows)"
            $targetArch = Read-Host "请输入目标架构 (如: arm, arm64)"
            $outputName = Read-Host "请输入输出文件名"
            Build-ForTarget $targetOS $targetArch $outputName
        }
        default {
            Write-Host "❌ 无效选择" -ForegroundColor Red
            return $false
        }
    }
    
    return $true
}

# 创建ARM配置文件
function New-ARMConfig {
    Write-Host "`n⚙️ 创建ARM优化配置..." -ForegroundColor Yellow
    
    $config = @{
        "Tcp_timeout" = 300
        "Udp_timeout" = 30
        "Listen_addr" = @(":1254")
        "proxy_key" = "Host"
        "encrypt_password" = "password"
        "Enable_dns_tcpOverUdp" = $true
        "Enable_httpDNS" = $true
        "Enable_TFO" = $false
        "Enable_traffic_control" = $true
        "API_port" = ":8080"
        "Tls" = @{
            "listen_addr" = @(":8978")
            "AutoCertHosts" = @("localhost")
        }
    }
    
    $configJson = $config | ConvertTo-Json -Depth 3
    $configJson | Out-File -FilePath "config_arm.json" -Encoding UTF8
    
    Write-Host "✅ ARM配置文件已创建: config_arm.json" -ForegroundColor Green
}

# 创建Linux启动脚本
function New-LinuxStartupScript {
    Write-Host "`n📝 创建Linux启动脚本..." -ForegroundColor Yellow
    
    $startupScript = @'
#!/bin/bash

echo "🚀 启动 CNS 流量控制系统 (ARM Linux)"
echo "===================================="

# 检查可执行文件
if [ -f "cns-arm64-linux" ]; then
    CNS_BINARY="./cns-arm64-linux"
elif [ -f "cns-arm32-linux" ]; then
    CNS_BINARY="./cns-arm32-linux"
else
    echo "❌ 未找到ARM Linux可执行文件"
    echo "请先运行编译脚本"
    exit 1
fi

# 设置执行权限
chmod +x $CNS_BINARY

# 检查配置文件
if [ -f "config_arm.json" ]; then
    CONFIG_FILE="config_arm.json"
elif [ -f "config.json" ]; then
    CONFIG_FILE="config.json"
else
    echo "❌ 未找到配置文件"
    exit 1
fi

echo "📊 服务信息:"
echo "   可执行文件: $CNS_BINARY"
echo "   配置文件: $CONFIG_FILE"
echo "   代理服务: localhost:1254"
echo "   API服务: http://localhost:8080"
echo "   Web管理: http://localhost:8081"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
$CNS_BINARY -json=$CONFIG_FILE
'@
    
    $startupScript | Out-File -FilePath "start_arm_linux.sh" -Encoding UTF8
    Write-Host "✅ Linux启动脚本已创建: start_arm_linux.sh" -ForegroundColor Green
}

# 创建Windows启动脚本
function New-WindowsStartupScript {
    Write-Host "`n📝 创建Windows启动脚本..." -ForegroundColor Yellow
    
    $startupScript = @'
@echo off
chcp 65001 >nul
echo 🚀 启动 CNS 流量控制系统 (ARM Windows)
echo ====================================

REM 检查可执行文件
if exist "cns-arm64-windows.exe" (
    set CNS_BINARY=cns-arm64-windows.exe
) else if exist "cns-arm32-windows.exe" (
    set CNS_BINARY=cns-arm32-windows.exe
) else (
    echo ❌ 未找到ARM Windows可执行文件
    echo 请先运行编译脚本
    pause
    exit /b 1
)

REM 检查配置文件
if exist "config_arm.json" (
    set CONFIG_FILE=config_arm.json
) else if exist "config.json" (
    set CONFIG_FILE=config.json
) else (
    echo ❌ 未找到配置文件
    pause
    exit /b 1
)

echo 📊 服务信息:
echo    可执行文件: %CNS_BINARY%
echo    配置文件: %CONFIG_FILE%
echo    代理服务: localhost:1254
echo    API服务: http://localhost:8080
echo    Web管理: http://localhost:8081
echo.
echo 💡 提示: 按 Ctrl+C 停止服务
echo.

REM 启动服务
%CNS_BINARY% -json=%CONFIG_FILE%

pause
'@
    
    $startupScript | Out-File -FilePath "start_arm_windows.bat" -Encoding UTF8
    Write-Host "✅ Windows启动脚本已创建: start_arm_windows.bat" -ForegroundColor Green
}

# 显示使用说明
function Show-Usage {
    Write-Host "`n📋 编译完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 使用方法:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Linux ARM设备:" -ForegroundColor Yellow
    Write-Host "1. 将以下文件传输到ARM设备:"
    Write-Host "   - cns-arm64-linux 或 cns-arm32-linux"
    Write-Host "   - config_arm.json"
    Write-Host "   - start_arm_linux.sh"
    Write-Host ""
    Write-Host "2. 在ARM设备上运行:"
    Write-Host "   chmod +x start_arm_linux.sh"
    Write-Host "   ./start_arm_linux.sh"
    Write-Host ""
    Write-Host "Windows ARM设备:" -ForegroundColor Yellow
    Write-Host "1. 将以下文件传输到ARM设备:"
    Write-Host "   - cns-arm64-windows.exe 或 cns-arm32-windows.exe"
    Write-Host "   - config_arm.json"
    Write-Host "   - start_arm_windows.bat"
    Write-Host ""
    Write-Host "2. 在ARM设备上运行:"
    Write-Host "   start_arm_windows.bat"
    Write-Host ""
    Write-Host "访问地址:" -ForegroundColor Cyan
    Write-Host "- Web管理: http://设备IP:8081"
    Write-Host "- API服务: http://设备IP:8080"
    Write-Host "- 代理服务: 设备IP:1254"
    Write-Host ""
    Write-Host "💡 提示:" -ForegroundColor Yellow
    Write-Host "- 确保ARM设备防火墙允许相应端口"
    Write-Host "- 建议在后台运行服务"
    Write-Host "- 查看详细文档: TRAFFIC_CONTROL_README.md"
}

# 主函数
function Main {
    Write-Host "欢迎使用 CNS ARM 编译脚本！" -ForegroundColor Cyan
    Write-Host "此脚本将帮助您为ARM设备编译CNS流量控制系统。" -ForegroundColor Cyan
    Write-Host ""
    
    # 检查Go环境
    if (-not (Test-GoEnvironment)) {
        return
    }
    
    # 显示系统信息
    $systemInfo = Get-SystemInfo
    
    # 安装依赖
    if (-not (Install-Dependencies)) {
        return
    }
    
    # 创建配置文件
    New-ARMConfig
    
    # 显示编译菜单并编译
    if (Show-BuildMenu) {
        # 创建启动脚本
        New-LinuxStartupScript
        New-WindowsStartupScript
        
        # 显示使用说明
        Show-Usage
        
        Write-Host "`n✅ ARM编译完成！" -ForegroundColor Green
    }
}

# 运行主函数
Main
