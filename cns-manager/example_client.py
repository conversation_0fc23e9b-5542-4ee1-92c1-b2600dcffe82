#!/usr/bin/env python3
"""
CNS 流量控制系统客户端示例
演示如何使用API进行用户管理和流量查询
"""

import requests
import json
import time
import base64
from datetime import datetime

class CNSClient:
    def __init__(self, api_base_url="http://localhost:8080"):
        self.api_base_url = api_base_url
        self.session = requests.Session()
    
    def add_user(self, username, password, quota_gb=1):
        """添加用户"""
        url = f"{self.api_base_url}/api/user/add"
        data = {
            "username": username,
            "password": password,
            "quota": quota_gb * 1024 * 1024 * 1024  # 转换为字节
        }
        
        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            print(f"✅ 用户 {username} 添加成功，用户ID: {result['user_id']}")
            return result['user_id']
        except requests.exceptions.RequestException as e:
            print(f"❌ 添加用户失败: {e}")
            return None
    
    def login_user(self, username, password):
        """用户登录验证"""
        url = f"{self.api_base_url}/api/auth/login"
        data = {
            "username": username,
            "password": password
        }
        
        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            print(f"✅ 用户 {username} 登录成功")
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 用户登录失败: {e}")
            return None
    
    def get_user_info(self, user_id):
        """获取用户信息"""
        url = f"{self.api_base_url}/api/user/info/{user_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()
            
            print(f"📊 用户信息:")
            print(f"   用户名: {result['username']}")
            print(f"   配额: {result['quota'] / 1024 / 1024 / 1024:.2f} GB")
            print(f"   已上传: {result['upload'] / 1024 / 1024:.2f} MB")
            print(f"   已下载: {result['download'] / 1024 / 1024:.2f} MB")
            print(f"   状态: {'正常' if result['status'] == 1 else '禁用'}")
            if result['expire_at']:
                print(f"   过期时间: {result['expire_at']}")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取用户信息失败: {e}")
            return None
    
    def get_user_traffic(self, user_id):
        """获取用户流量信息"""
        url = f"{self.api_base_url}/api/user/traffic"
        params = {"user_id": user_id}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            
            print(f"📈 流量统计:")
            print(f"   上传: {result['upload'] / 1024 / 1024:.2f} MB")
            print(f"   下载: {result['download'] / 1024 / 1024:.2f} MB")
            print(f"   配额: {result['quota'] / 1024 / 1024 / 1024:.2f} GB")
            print(f"   使用率: {result['used_percent']:.1f}%")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取流量信息失败: {e}")
            return None
    
    def get_realtime_stats(self, user_id):
        """获取实时流量统计"""
        url = f"{self.api_base_url}/api/stats/realtime/{user_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()
            
            print(f"⚡ 实时统计:")
            print(f"   实时上传: {result['realtime_upload'] / 1024:.2f} KB")
            print(f"   实时下载: {result['realtime_download'] / 1024:.2f} KB")
            print(f"   总上传: {result['total_upload'] / 1024 / 1024:.2f} MB")
            print(f"   总下载: {result['total_download'] / 1024 / 1024:.2f} MB")
            print(f"   剩余配额: {result['remaining'] / 1024 / 1024 / 1024:.2f} GB")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取实时统计失败: {e}")
            return None
    
    def get_daily_stats(self, user_id, days=7):
        """获取每日流量统计"""
        url = f"{self.api_base_url}/api/stats/daily/{user_id}"
        params = {"days": days}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            
            print(f"📅 每日统计 (最近{days}天):")
            for stat in result['stats']:
                upload_mb = stat['upload'] / 1024 / 1024
                download_mb = stat['download'] / 1024 / 1024
                total_mb = stat['total'] / 1024 / 1024
                print(f"   {stat['date']}: 上传 {upload_mb:.1f}MB, 下载 {download_mb:.1f}MB, 总计 {total_mb:.1f}MB, 连接 {stat['connections']}次")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取每日统计失败: {e}")
            return None
    
    def reset_user_traffic(self, user_id):
        """重置用户流量"""
        url = f"{self.api_base_url}/api/user/reset"
        params = {"user_id": user_id}
        
        try:
            response = self.session.post(url, params=params)
            response.raise_for_status()
            result = response.json()
            
            if result['success']:
                print(f"✅ 用户流量重置成功")
            else:
                print(f"❌ 用户流量重置失败")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 重置用户流量失败: {e}")
            return None
    
    def set_user_expire(self, user_id, expire_date):
        """设置用户过期时间"""
        url = f"{self.api_base_url}/api/user/expire/{user_id}"
        data = {"expire_at": expire_date}
        
        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            
            if result['success']:
                print(f"✅ 用户过期时间设置成功: {expire_date}")
            else:
                print(f"❌ 用户过期时间设置失败")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 设置用户过期时间失败: {e}")
            return None
    
    def get_system_status(self):
        """获取系统状态"""
        url = f"{self.api_base_url}/api/system/status"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()
            
            print(f"🖥️  系统状态:")
            print(f"   在线用户: {result['online_users']}")
            print(f"   总用户数: {result['total_users']}")
            print(f"   服务器时间: {result['server_time']}")
            print(f"   运行时间: {result['uptime']}")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取系统状态失败: {e}")
            return None
    
    def list_users(self, page=1, limit=10):
        """获取用户列表"""
        url = f"{self.api_base_url}/api/system/users"
        params = {"page": page, "limit": limit}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            
            print(f"👥 用户列表 (第{page}页, 共{result['total_pages']}页):")
            for user in result['users']:
                quota_gb = user['quota'] / 1024 / 1024 / 1024
                used_gb = (user['upload'] + user['download']) / 1024 / 1024 / 1024
                status = '正常' if user['status'] == 1 else '禁用'
                print(f"   {user['username']}: {used_gb:.2f}/{quota_gb:.2f}GB ({user['used_percent']:.1f}%) - {status}")
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取用户列表失败: {e}")
            return None

def main():
    """示例使用"""
    print("🚀 CNS 流量控制系统客户端示例")
    print("=" * 50)
    
    client = CNSClient()
    
    # 1. 获取系统状态
    print("\n1. 获取系统状态")
    client.get_system_status()
    
    # 2. 添加测试用户
    print("\n2. 添加测试用户")
    username = f"testuser_{int(time.time())}"
    password = "testpass123"
    user_id = client.add_user(username, password, quota_gb=2)
    
    if user_id:
        # 3. 用户登录验证
        print("\n3. 用户登录验证")
        login_result = client.login_user(username, password)
        
        # 4. 获取用户信息
        print("\n4. 获取用户信息")
        client.get_user_info(user_id)
        
        # 5. 获取流量信息
        print("\n5. 获取流量信息")
        client.get_user_traffic(user_id)
        
        # 6. 获取实时统计
        print("\n6. 获取实时统计")
        client.get_realtime_stats(user_id)
        
        # 7. 获取每日统计
        print("\n7. 获取每日统计")
        client.get_daily_stats(user_id, days=7)
        
        # 8. 设置过期时间
        print("\n8. 设置用户过期时间")
        expire_date = "2024-12-31 23:59:59"
        client.set_user_expire(user_id, expire_date)
    
    # 9. 获取用户列表
    print("\n9. 获取用户列表")
    client.list_users(page=1, limit=5)
    
    print("\n✨ 示例完成!")

if __name__ == "__main__":
    main()
